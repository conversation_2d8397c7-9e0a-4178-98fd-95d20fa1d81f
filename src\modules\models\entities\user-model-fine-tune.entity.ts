import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng user_model_fine_tune trong cơ sở dữ liệu
 * Quản lý các model fine-tune của user
 */
@Entity('user_model_fine_tune')
export class UserModelFineTune {
  /**
   * UUID của user model fine tune
   */
  @PrimaryGeneratedColumn('uuid')
  id: string;

  /**
   * ID định danh của model
   */
  @Column({
    name: 'model_id',
    type: 'varchar',
    length: 255,
    comment: 'ID định danh của model'
  })
  modelId: string;

  /**
   * Model cơ sở được sử dụng để fine-tune
   */
  @Column({
    name: 'model_base',
    type: 'varchar',
    length: 255,
    comment: 'Model cơ sở được sử dụng để fine-tune'
  })
  modelBase: string;

  /**
   * Liên kết đến bảng model_registry
   */
  @Column({
    name: 'model_registry_id',
    type: 'uuid',
    comment: '<PERSON><PERSON>n kết đến bảng model_registry'
  })
  modelRegistryId: string;

  /**
   * <PERSON><PERSON><PERSON> kết đến bảng user_key_llm
   */
  @Column({
    name: 'llm_key_id',
    type: 'uuid',
    comment: 'Liên kết đến bảng user_key_llm'
  })
  llmKeyId: string;

  /**
   * Liên kết đến bảng fine_tune_histories
   */
  @Column({
    name: 'detail_id',
    type: 'uuid',
    comment: 'Liên kết đến bảng fine_tune_histories'
  })
  detailId: string;
}
