# Hướng Dẫn Sử Dụng Fine-Tuning Jobs

Tài liệu này mô tả các hàm fine-tuning đã được thêm vào `OpenAiService` và `GoogleAIService`.

## OpenAI Fine-Tuning

### 1. Upload Training File
```typescript
async uploadTrainingFile(fileKey: string, apiKey: string): Promise<string>
```
- **<PERSON><PERSON><PERSON> đích**: Upload file dữ liệu huấn luyện (JSONL) từ S3 lên OpenAI
- **Tham số**:
  - `fileKey`: S3 key của file dữ liệu huấn luyện
  - `apiKey`: API key của OpenAI
- **Trả về**: ID của file đã upload trên OpenAI

### 2. Create Fine-Tuning Job
```typescript
async createFineTuningJob(params: CreateFineTuningJobParams, apiKey: string): Promise<FineTuningJobResponse>
```
- **<PERSON><PERSON><PERSON> đ<PERSON>**: Tạo fine-tuning job mới
- **Tham số**:
  - `params.trainingFileId`: ID của file training data
  - `params.model`: Model cơ sở (ví dụ: "gpt-3.5-turbo")
  - `params.suffix`: Hậu tố cho model name (tùy chọn)
  - `params.hyperparameters`: Siêu tham số (tùy chọn)
  - `params.validationFileId`: ID của file validation data (tùy chọn)
- **Trả về**: Thông tin chi tiết về job đã tạo

### 3. Get Fine-Tuning Job
```typescript
async getFineTuningJob(jobId: string, apiKey: string): Promise<FineTuningJobResponse>
```
- **Mục đích**: Lấy thông tin chi tiết về fine-tuning job
- **Tham số**:
  - `jobId`: ID của fine-tuning job
  - `apiKey`: API key của OpenAI
- **Trả về**: Thông tin chi tiết về job

### 4. List Fine-Tuning Jobs
```typescript
async listFineTuningJobs(apiKey: string, limit?: number, after?: string): Promise<FineTuningJobResponse[]>
```
- **Mục đích**: Lấy danh sách fine-tuning jobs
- **Tham số**:
  - `apiKey`: API key của OpenAI
  - `limit`: Số lượng job tối đa (mặc định: 20)
  - `after`: ID của job để phân trang (tùy chọn)
- **Trả về**: Mảng các fine-tuning jobs

### 5. Cancel Fine-Tuning Job
```typescript
async cancelFineTuningJob(jobId: string, apiKey: string): Promise<FineTuningJobResponse>
```
- **Mục đích**: Hủy fine-tuning job đang chạy
- **Tham số**:
  - `jobId`: ID của fine-tuning job cần hủy
  - `apiKey`: API key của OpenAI
- **Trả về**: Thông tin về job đã hủy

## Google AI Fine-Tuning

### 1. Upload Training File
```typescript
async uploadTrainingFile(fileKey: string, bucketName: string, apiKey: string): Promise<string>
```
- **Mục đích**: Upload file dữ liệu huấn luyện từ S3 lên Google Cloud Storage
- **Tham số**:
  - `fileKey`: S3 key của file dữ liệu huấn luyện
  - `bucketName`: Tên bucket Google Cloud Storage
  - `apiKey`: Service Account Key của Google Cloud
- **Trả về**: URI của file trong Google Cloud Storage (gs://bucket/file)

### 2. Create Fine-Tuning Job
```typescript
async createFineTuningJob(params: GoogleAIFineTuningParams, apiKey: string, projectId: string, location?: string): Promise<GoogleAIFineTuningResponse>
```
- **Mục đích**: Tạo fine-tuning job mới trên Vertex AI
- **Tham số**:
  - `params.baseModelId`: ID của model cơ sở (ví dụ: "gemini-1.5-pro")
  - `params.displayName`: Tên hiển thị cho model fine-tuned
  - `params.trainingDataUri`: URI của file training data trong GCS
  - `params.description`: Mô tả (tùy chọn)
  - `params.validationDataUri`: URI của file validation data (tùy chọn)
  - `params.hyperParameters`: Siêu tham số (tùy chọn)
  - `projectId`: ID của Google Cloud Project
  - `location`: Location (mặc định: "us-central1")
- **Trả về**: Thông tin chi tiết về job đã tạo

### 3. Get Fine-Tuning Job
```typescript
async getFineTuningJob(jobName: string, apiKey: string): Promise<GoogleAIFineTuningResponse>
```
- **Mục đích**: Lấy thông tin chi tiết về fine-tuning job
- **Tham số**:
  - `jobName`: Tên đầy đủ của job (projects/{project}/locations/{location}/tuningJobs/{job})
  - `apiKey`: Service Account Key của Google Cloud
- **Trả về**: Thông tin chi tiết về job

### 4. List Fine-Tuning Jobs
```typescript
async listFineTuningJobs(projectId: string, location: string, apiKey: string, pageSize?: number, pageToken?: string): Promise<{jobs: GoogleAIFineTuningResponse[], nextPageToken?: string}>
```
- **Mục đích**: Lấy danh sách fine-tuning jobs
- **Tham số**:
  - `projectId`: ID của Google Cloud Project
  - `location`: Location (ví dụ: "us-central1")
  - `apiKey`: Service Account Key của Google Cloud
  - `pageSize`: Số lượng job tối đa (mặc định: 20)
  - `pageToken`: Token để phân trang (tùy chọn)
- **Trả về**: Object chứa mảng jobs và nextPageToken

### 5. Cancel Fine-Tuning Job
```typescript
async cancelFineTuningJob(jobName: string, apiKey: string): Promise<GoogleAIFineTuningResponse>
```
- **Mục đích**: Hủy fine-tuning job đang chạy
- **Tham số**:
  - `jobName`: Tên đầy đủ của job cần hủy
  - `apiKey`: Service Account Key của Google Cloud
- **Trả về**: Thông tin về job đã hủy

## Ví Dụ Sử Dụng

### OpenAI Fine-Tuning Workflow
```typescript
// 1. Upload training file
const fileId = await openaiService.uploadTrainingFile('path/to/training.jsonl', apiKey);

// 2. Create fine-tuning job
const job = await openaiService.createFineTuningJob({
  trainingFileId: fileId,
  model: 'gpt-3.5-turbo',
  suffix: 'my-model',
  hyperparameters: {
    nEpochs: 3,
    batchSize: 'auto',
    learningRateMultiplier: 'auto'
  }
}, apiKey);

// 3. Monitor job status
const jobStatus = await openaiService.getFineTuningJob(job.id, apiKey);

// 4. List all jobs
const jobs = await openaiService.listFineTuningJobs(apiKey, 10);

// 5. Cancel job if needed
if (jobStatus.status === 'running') {
  await openaiService.cancelFineTuningJob(job.id, apiKey);
}
```

### Google AI Fine-Tuning Workflow
```typescript
// 1. Upload training file to GCS
const gcsUri = await googleAIService.uploadTrainingFile('path/to/training.jsonl', 'my-bucket', serviceAccountKey);

// 2. Create fine-tuning job
const job = await googleAIService.createFineTuningJob({
  baseModelId: 'gemini-1.5-pro',
  displayName: 'My Fine-Tuned Model',
  trainingDataUri: gcsUri,
  hyperParameters: {
    epochCount: 3,
    batchSize: 4,
    learningRate: 1e-5
  }
}, serviceAccountKey, 'my-project-id', 'us-central1');

// 3. Monitor job status
const jobStatus = await googleAIService.getFineTuningJob(job.name, serviceAccountKey);

// 4. List all jobs
const { jobs } = await googleAIService.listFineTuningJobs('my-project-id', 'us-central1', serviceAccountKey, 10);

// 5. Cancel job if needed
if (jobStatus.state === 'JOB_STATE_RUNNING') {
  await googleAIService.cancelFineTuningJob(job.name, serviceAccountKey);
}
```

## Lưu Ý Quan Trọng

1. **File Format**: Cả OpenAI và Google AI đều yêu cầu file training data ở định dạng JSONL
2. **Authentication**: OpenAI sử dụng API key, Google AI sử dụng Service Account Key
3. **Storage**: OpenAI tự quản lý file storage, Google AI yêu cầu upload lên Google Cloud Storage
4. **Monitoring**: Cần theo dõi trạng thái job thường xuyên vì quá trình fine-tuning có thể mất nhiều thời gian
5. **Error Handling**: Tất cả các hàm đều có xử lý lỗi chi tiết với AppException

## Status Mapping

### OpenAI Job Status
- `validating_files`: Đang xác thực file
- `queued`: Đang chờ trong hàng đợi
- `running`: Đang chạy
- `succeeded`: Hoàn thành thành công
- `failed`: Thất bại
- `cancelled`: Đã hủy

### Google AI Job State
- `JOB_STATE_QUEUED`: Đang chờ trong hàng đợi
- `JOB_STATE_RUNNING`: Đang chạy
- `JOB_STATE_SUCCEEDED`: Hoàn thành thành công
- `JOB_STATE_FAILED`: Thất bại
- `JOB_STATE_CANCELLED`: Đã hủy
