import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng user_models trong cơ sở dữ liệu
 * Quản lý các model mà user có thể sử dụng
 */
@Entity('user_models')
export class UserModels {
  /**
   * UUID của user model
   */
  @PrimaryGeneratedColumn('uuid')
  id: string;

  /**
   * ID định danh của model
   */
  @Column({
    name: 'model_id',
    type: 'varchar',
    length: 255,
    unique: true,
    comment: 'ID định danh của model'
  })
  modelId: string;

  /**
   * Liên kết đến bảng model_registry
   */
  @Column({
    name: 'model_registry_id',
    type: 'uuid',
    comment: 'Liên kết đến bảng model_registry'
  })
  modelRegistryId: string;
}
