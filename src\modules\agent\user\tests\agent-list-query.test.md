# Test Agent List Query

## <PERSON><PERSON> tả
Test file để kiểm tra truy vấn danh sách agent với SQL query mới.

## SQL Query được implement
```sql
SELECT "agent"."id"                       AS "agent_id",
       "agent"."name"                     AS "agent_name",
       "agent"."avatar"                   AS "agent_avatar",
       "agentUser"."active"               AS "agentUser_active",
       "agentUser"."exp"                  AS "agentUser_exp",
       "typeAgent"."name"                 AS "typeAgent_name",
       "agentRank"."id"                   AS "agentRank_id",
       "agentRank"."name"                 AS "agentRank_name",
       "agentRank"."badge"                AS "agentRank_badge",
       "agent"."created_at",
       "agent"."updated_at",
       "agentUser"."type_id",
       "agentUser"."user_model_id",
       "agentUser"."system_model_id",
       "agentUser"."model_fine_tune_id",
       "userModel"."model_id",
       "systemModel"."model_id",
       "userModelFineTune"."model_id",
       "agentRank"."max_exp"
FROM "agents" "agent"
         INNER JOIN "agents_user" "agentUser" ON "agentUser"."id" = "agent"."id"
         LEFT JOIN "type_agents" "typeAgent" ON "agentUser"."type_id" = "typeAgent"."id"
         LEFT JOIN "user_models" "userModel" ON "agentUser"."user_model_id" = "userModel"."id"
         LEFT JOIN "system_models" "systemModel" ON "agentUser"."system_model_id" = "systemModel"."id"
         LEFT JOIN "user_model_fine_tune" "userModelFineTune"
                   ON "agentUser"."model_fine_tune_id" = "userModelFineTune"."id"
         LEFT JOIN "agents_rank" "agentRank"
                   ON "agentUser"."exp" >= "agentRank"."min_exp" AND "agentUser"."exp" < "agentRank"."max_exp" AND
                      "agentRank"."active" = true
WHERE "agentUser"."user_id" = 38
  AND "agent"."deleted_at" IS NULL
ORDER BY "agent"."created_at" DESC
LIMIT 10
```

## Các thành phần đã implement

### 1. Repository Method
- `AgentUserRepository.getAgentsList(userId, queryDto)`
- Trả về `PaginatedResult<AgentListRawResult>`
- Hỗ trợ phân trang, search, filter theo typeId và active

### 2. Mapper
- `AgentListMapper.toDto(rawData)` - chuyển đổi 1 record
- `AgentListMapper.toDtoList(rawDataList)` - chuyển đổi mảng records
- Resolve model_id theo priority: userModel → systemModel → fineTuneModel
- Chuyển đổi S3 keys thành URLs đầy đủ cho avatar và badge

### 3. Service Method
- `AgentUserService.getAgents(userId, queryDto)`
- Gọi repository và mapper
- Trả về `PaginatedResult<AgentListItemDto>`

### 4. DTO Updates
- `AgentQueryDto` đã thêm `typeId` và `active` filters
- Sử dụng đúng cấu trúc `PaginationMeta` từ `@common/response`

## Test Cases cần kiểm tra

1. **Basic Query**: Lấy danh sách agent cơ bản
2. **Pagination**: Test phân trang với page/limit khác nhau
3. **Search**: Test tìm kiếm theo tên agent
4. **Filter by Type**: Test filter theo typeId
5. **Filter by Active**: Test filter theo trạng thái active
6. **Model Resolution**: Test resolve model_id từ các nguồn khác nhau
7. **Avatar/Badge URLs**: Test chuyển đổi S3 keys thành URLs

## Cách test

```typescript
// Test trong controller hoặc service test
const result = await agentUserService.getAgents(userId, {
  page: 1,
  limit: 10,
  search: 'test',
  typeId: 1,
  active: true
});

console.log('Items:', result.items);
console.log('Meta:', result.meta);
```
