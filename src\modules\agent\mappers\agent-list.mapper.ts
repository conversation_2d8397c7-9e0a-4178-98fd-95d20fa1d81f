import { Injectable, Logger } from '@nestjs/common';
import { AgentListItemDto } from '@modules/agent/user/dto/agent/agent-response.dto';
import { AgentListRawResult } from '@modules/agent/repositories/agent-user.repository';
import { CdnService } from '@/shared/services/cdn.service';
import { TimeIntervalEnum } from '@/shared/utils';

/**
 * Mapper để chuyển đổi dữ liệu raw từ database sang DTO cho danh sách agent
 */
@Injectable()
export class AgentListMapper {
  private readonly logger = new Logger(AgentListMapper.name);

  constructor(private readonly cdnService: CdnService) {}

  /**
   * <PERSON>y<PERSON><PERSON> đổi từ AgentListRawResult sang AgentListItemDto
   * @param rawData Dữ liệu raw từ database
   * @returns AgentListItemDto
   */
  toDto(rawData: AgentListRawResult): AgentListItemDto {
    try {
      // Resolve model_id theo priority: userModel → systemModel → fineTuneModel
      let resolvedModelId: string | undefined;
      
      if (rawData.userModel_model_id) {
        resolvedModelId = rawData.userModel_model_id;
      } else if (rawData.systemModel_model_id) {
        resolvedModelId = rawData.systemModel_model_id;
      } else if (rawData.userModelFineTune_model_id) {
        resolvedModelId = rawData.userModelFineTune_model_id;
      }

      // Chuyển đổi avatar từ S3 key sang URL đầy đủ
      const avatarUrl = rawData.agent_avatar 
        ? this.cdnService.generateUrlView(rawData.agent_avatar, TimeIntervalEnum.FIVE_MINUTES)
        : null;

      // Chuyển đổi badge từ S3 key sang URL đầy đủ
      const badgeUrl = rawData.agentRank_badge 
        ? this.cdnService.generateUrlView(rawData.agentRank_badge, TimeIntervalEnum.FIVE_MINUTES)
        : null;

      // Chuyển đổi timestamp sang millis
      const createdAt = new Date(rawData.created_at).getTime();
      const updatedAt = new Date(rawData.updated_at).getTime();

      return {
        id: rawData.agent_id,
        name: rawData.agent_name,
        avatar: avatarUrl,
        typeId: rawData.agentUser_type_id,
        typeName: rawData.typeAgent_name || '',
        exp: rawData.agentUser_exp,
        expMax: rawData.agentRank_max_exp || 0,
        level: rawData.agentRank_id || 1,
        badgeUrl: badgeUrl,
        modelId: resolvedModelId,
        active: rawData.agentUser_active,
        createdAt: createdAt,
        updatedAt: updatedAt,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi chuyển đổi dữ liệu agent: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Chuyển đổi mảng AgentListRawResult sang mảng AgentListItemDto
   * @param rawDataList Mảng dữ liệu raw từ database
   * @returns Mảng AgentListItemDto
   */
  toDtoList(rawDataList: AgentListRawResult[]): AgentListItemDto[] {
    return rawDataList.map(rawData => this.toDto(rawData));
  }
}
