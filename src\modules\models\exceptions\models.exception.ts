import { HttpStatus } from '@nestjs/common';
import { ErrorCode } from '@common/exceptions';

/**
 * Error codes cho Models module (20000-20099)
 */
export const MODELS_ERROR_CODES = {

  // User Data Fine Tune errors (20100-20199)
  USER_DATA_FINE_TUNE_NOT_FOUND: new ErrorCode(
    20100,
    'Dataset fine tune không tồn tại',
    HttpStatus.NOT_FOUND,
  ),
  USER_DATA_FINE_TUNE_NAME_EXISTS: new ErrorCode(
    20101,
    'Tên dataset fine tune đã tồn tại',
    HttpStatus.BAD_REQUEST,
  ),
  USER_DATA_FINE_TUNE_INVALID_TRAINING_DATA: new ErrorCode(
    20102,
    'Dữ liệu training không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),
  USER_DATA_FINE_TUNE_INVALID_STATUS: new ErrorCode(
    20103,
    'Trạng thái dataset fine tune không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),
  USER_DATA_FINE_TUNE_TRAIN_FILE_NOT_FOUND: new ErrorCode(
    20104,
    'File training dataset không tồn tại trên S3',
    HttpStatus.BAD_REQUEST,
  ),
  USER_DATA_FINE_TUNE_VALID_FILE_NOT_FOUND: new ErrorCode(
    20105,
    'File validation dataset không tồn tại trên S3',
    HttpStatus.BAD_REQUEST,
  ),
  ADMIN_DATA_FINE_TUNE_INVALID_STATUS_TRANSITION: new ErrorCode(
    20106,
    'Chỉ có thể cập nhật trạng thái từ PENDING',
    HttpStatus.BAD_REQUEST,
  ),
  ADMIN_DATA_FINE_TUNE_INVALID_TARGET_STATUS: new ErrorCode(
    20107,
    'Chỉ có thể cập nhật trạng thái thành APPROVED hoặc ERROR',
    HttpStatus.BAD_REQUEST,
  ),
  ADMIN_DATA_FINE_TUNE_TRAIN_FILE_NOT_FOUND: new ErrorCode(
    20108,
    'File training dataset không tồn tại trên S3',
    HttpStatus.BAD_REQUEST,
  ),
  ADMIN_DATA_FINE_TUNE_VALID_FILE_NOT_FOUND: new ErrorCode(
    20109,
    'File validation dataset không tồn tại trên S3',
    HttpStatus.BAD_REQUEST,
  ),

  // Admin Data Fine-Tune Errors (20000-20009)
  ADMIN_DATA_FINE_TUNE_NOT_FOUND: new ErrorCode(
    20000,
    'Không tìm thấy dataset fine-tune',
    HttpStatus.NOT_FOUND,
  ),

  ADMIN_DATA_FINE_TUNE_NAME_EXISTS: new ErrorCode(
    20001,
    'Tên dataset đã tồn tại',
    HttpStatus.BAD_REQUEST,
  ),

  ADMIN_DATA_FINE_TUNE_INVALID_TRAINING_DATA: new ErrorCode(
    20002,
    'Dữ liệu training không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  ADMIN_DATA_FINE_TUNE_INVALID_VALIDATION_DATA: new ErrorCode(
    20003,
    'Dữ liệu validation không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  ADMIN_DATA_FINE_TUNE_CREATION_FAILED: new ErrorCode(
    20004,
    'Tạo dataset fine-tune thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  ADMIN_DATA_FINE_TUNE_UPDATE_FAILED: new ErrorCode(
    20005,
    'Cập nhật dataset fine-tune thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  ADMIN_DATA_FINE_TUNE_DELETE_FAILED: new ErrorCode(
    20006,
    'Xóa dataset fine-tune thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  ADMIN_DATA_FINE_TUNE_RESTORE_FAILED: new ErrorCode(
    20007,
    'Khôi phục dataset fine-tune thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  // Model Registry Errors (20010-20019)
  MODEL_REGISTRY_NOT_FOUND: new ErrorCode(
    20010,
    'Không tìm thấy model registry',
    HttpStatus.NOT_FOUND,
  ),

  MODEL_REGISTRY_PATTERN_EXISTS: new ErrorCode(
    20011,
    'Pattern model đã tồn tại',
    HttpStatus.BAD_REQUEST,
  ),

  MODEL_REGISTRY_INVALID_PATTERN: new ErrorCode(
    20012,
    'Pattern model không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  MODEL_REGISTRY_DELETE_FAILED: new ErrorCode(
    20013,
    'Xóa model registry thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  MODEL_REGISTRY_RESTORE_FAILED: new ErrorCode(
    20014,
    'Khôi phục model registry thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  MODEL_BASE_NOT_FOUND: new ErrorCode(
    20015,
    'Model base không tồn tại',
    HttpStatus.NOT_FOUND,
  ),

  MODEL_BASE_NAME_EXISTS: new ErrorCode(
    20016,
    'Tên model base đã tồn tại',
    HttpStatus.CONFLICT,
  ),

  MODEL_BASE_MODEL_ID_EXISTS: new ErrorCode(
    20017,
    'Model ID đã tồn tại cho provider này',
    HttpStatus.CONFLICT,
  ),

  MODEL_BASE_DELETE_FAILED: new ErrorCode(
    20018,
    'Xóa model base thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  MODEL_BASE_RESTORE_FAILED: new ErrorCode(
    20019,
    'Khôi phục model base thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  MODEL_BASE_INVALID_CONFIG: new ErrorCode(
    20022,
    'Cấu hình model base không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  MODEL_BASE_SYSTEM_KEY_NOT_FOUND: new ErrorCode(
    20023,
    'Không tìm thấy system key LLM',
    HttpStatus.BAD_REQUEST,
  ),

  // System Key LLM Errors (20030-20039)
  SYSTEM_KEY_LLM_NOT_FOUND: new ErrorCode(
    20030,
    'Không tìm thấy system key LLM',
    HttpStatus.NOT_FOUND,
  ),

  SYSTEM_KEY_LLM_NAME_EXISTS: new ErrorCode(
    20031,
    'Tên system key đã tồn tại',
    HttpStatus.BAD_REQUEST,
  ),

  SYSTEM_KEY_LLM_INVALID_KEY: new ErrorCode(
    20032,
    'API key không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  SYSTEM_KEY_LLM_ENCRYPTION_FAILED: new ErrorCode(
    20033,
    'Mã hóa API key thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  SYSTEM_KEY_LLM_DELETE_FAILED: new ErrorCode(
    20034,
    'Xóa system key LLM thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  SYSTEM_KEY_LLM_RESTORE_FAILED: new ErrorCode(
    20035,
    'Khôi phục system key LLM thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  SYSTEM_KEY_LLM_UPDATE_FAILED: new ErrorCode(
    20036,
    'Cập nhật system key LLM thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  USER_KEY_LLM_NOT_FOUND: new ErrorCode(
    20037,
    'User key LLM không tồn tại',
    HttpStatus.NOT_FOUND,
  ),

  USER_KEY_LLM_NAME_EXISTS: new ErrorCode(
    20038,
    'Tên user key LLM đã tồn tại',
    HttpStatus.CONFLICT,
  ),

  USER_KEY_LLM_INVALID_NAME: new ErrorCode(
    20039,
    'Tên user key LLM không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  USER_KEY_LLM_INVALID_FORMAT: new ErrorCode(
    20040,
    'Format API key không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  USER_KEY_LLM_INVALID_BASE_URL: new ErrorCode(
    20041,
    'Base URL không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  USER_KEY_LLM_CONNECTION_FAILED: new ErrorCode(
    20042,
    'Test connection API key thất bại',
    HttpStatus.BAD_REQUEST,
  ),

  USER_KEY_LLM_DELETE_FAILED: new ErrorCode(
    20043,
    'Xóa user key LLM thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  USER_KEY_LLM_RESTORE_FAILED: new ErrorCode(
    20044,
    'Khôi phục user key LLM thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  // Data Fine-Tune Errors (20050-20059)
  DATA_FINE_TUNE_NOT_FOUND: new ErrorCode(
    20050,
    'Không tìm thấy dataset fine-tune',
    HttpStatus.NOT_FOUND,
  ),

  DATA_FINE_TUNE_NAME_EXISTS: new ErrorCode(
    20051,
    'Tên dataset đã tồn tại',
    HttpStatus.BAD_REQUEST,
  ),

  DATA_FINE_TUNE_INVALID_FILE: new ErrorCode(
    20052,
    'File dataset không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  DATA_FINE_TUNE_UPLOAD_FAILED: new ErrorCode(
    20053,
    'Upload dataset thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  // Model Fine-Tune Errors (20060-20069)
  MODEL_FINE_TUNE_NOT_FOUND: new ErrorCode(
    20060,
    'Không tìm thấy model fine-tune',
    HttpStatus.NOT_FOUND,
  ),

  MODEL_FINE_TUNE_CREATION_FAILED: new ErrorCode(
    20061,
    'Tạo fine-tune job thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  MODEL_FINE_TUNE_INVALID_STATUS: new ErrorCode(
    20062,
    'Trạng thái fine-tune không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  // General Model Errors (20070-20079)
  MODEL_VALIDATION_FAILED: new ErrorCode(
    20070,
    'Validation model thất bại',
    HttpStatus.BAD_REQUEST,
  ),

  MODEL_ACCESS_DENIED: new ErrorCode(
    20071,
    'Không có quyền truy cập model',
    HttpStatus.FORBIDDEN,
  ),

  MODEL_PROVIDER_ERROR: new ErrorCode(
    20072,
    'Lỗi từ nhà cung cấp model',
    HttpStatus.BAD_GATEWAY,
  ),

  MODEL_QUOTA_EXCEEDED: new ErrorCode(
    20073,
    'Đã vượt quá giới hạn sử dụng model',
    HttpStatus.TOO_MANY_REQUESTS,
  ),

  // Model Discovery Errors (20080-20089)
  MODEL_DISCOVERY_FAILED: new ErrorCode(
    20080,
    'Discovery models thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  PROVIDER_CONNECTION_FAILED: new ErrorCode(
    20081,
    'Kết nối đến provider thất bại',
    HttpStatus.BAD_GATEWAY,
  ),

  MODEL_PATTERN_FETCH_FAILED: new ErrorCode(
    20082,
    'Lấy model patterns thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  SYSTEM_KEY_LLM_INVALID_PROVIDER: new ErrorCode(
    20083,
    'Provider của system key không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  SYSTEM_KEY_LLM_DELETED: new ErrorCode(
    20084,
    'System key đã bị xóa',
    HttpStatus.BAD_REQUEST,
  ),

  USER_KEY_LLM_INVALID_PROVIDER: new ErrorCode(
    20085,
    'Provider của user key không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  USER_KEY_LLM_DELETED: new ErrorCode(
    20086,
    'User key đã bị xóa',
    HttpStatus.BAD_REQUEST,
  ),

  USER_KEY_LLM_INVALID_UUID: new ErrorCode(
    20089,
    'LLM Key ID không đúng định dạng UUID',
    HttpStatus.BAD_REQUEST,
  ),

  MODEL_SYNC_FAILED: new ErrorCode(
    20087,
    'Đồng bộ models thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  MODEL_PATTERN_MATCHING_FAILED: new ErrorCode(
    20088,
    'Pattern matching models thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  // Fine-Tuning Job Errors (20200-20219)
  DATASET_NOT_FOUND: new ErrorCode(
    20200,
    'Không tìm thấy dataset',
    HttpStatus.NOT_FOUND,
  ),

  MISSING_TRAINING_DATA: new ErrorCode(
    20201,
    'Dataset không có dữ liệu training',
    HttpStatus.BAD_REQUEST,
  ),

  MODEL_NOT_FOUND: new ErrorCode(
    20202,
    'Không tìm thấy model',
    HttpStatus.NOT_FOUND,
  ),

  INVALID_PROVIDER: new ErrorCode(
    20203,
    'Provider không khớp với model',
    HttpStatus.BAD_REQUEST,
  ),

  MISSING_API_KEY: new ErrorCode(
    20204,
    'Thiếu API key để thực hiện fine-tuning',
    HttpStatus.BAD_REQUEST,
  ),

  MISSING_GOOGLE_CLOUD_CONFIG: new ErrorCode(
    20205,
    'Thiếu thông tin Google Cloud configuration',
    HttpStatus.BAD_REQUEST,
  ),

  TOKEN_CALCULATION_FAILED: new ErrorCode(
    20206,
    'Không thể tính toán số lượng token',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  INSUFFICIENT_POINTS: new ErrorCode(
    20207,
    'Số dư R-Points không đủ',
    HttpStatus.PAYMENT_REQUIRED,
  ),

  FINE_TUNING_JOB_CREATION_FAILED: new ErrorCode(
    20208,
    'Tạo fine-tuning job thất bại',
    HttpStatus.UNPROCESSABLE_ENTITY,
  ),

  INVALID_INPUT: new ErrorCode(
    20209,
    'Dữ liệu đầu vào không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),
};


