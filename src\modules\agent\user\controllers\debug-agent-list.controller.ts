import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth } from '@nestjs/swagger';
import { JwtUserGuard } from '@shared/guards/jwt-user.guard';
import { CurrentUser } from '@shared/decorators/current-user.decorator';
import { User } from '@modules/user/entities/user.entity';
import { AgentUserService } from '../services/agent-user.service';
import { AgentQueryDto } from '../dto/agent/agent-query.dto';

/**
 * Controller tạm thời để debug agent list query
 */
@ApiTags('Debug Agent List')
@Controller('debug/agent-list')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class DebugAgentListController {
  constructor(private readonly agentUserService: AgentUserService) {}

  /**
   * Debug endpoint để test agent list query
   */
  @Get()
  @ApiOperation({
    summary: 'Debug agent list query',
    description: 'Test endpoint để debug agent list query và mapping'
  })
  async debugAgentList(
    @CurrentUser() user: User,
    @Query() queryDto: AgentQueryDto,
  ) {
    try {
      console.log('=== DEBUG AGENT LIST ===');
      console.log('User ID:', user.id);
      console.log('Query DTO:', queryDto);

      const result = await this.agentUserService.getAgents(user.id, queryDto);

      console.log('=== RESULT ===');
      console.log('Items count:', result.items.length);
      console.log('Meta:', result.meta);
      
      if (result.items.length > 0) {
        console.log('First item:', JSON.stringify(result.items[0], null, 2));
      }

      return {
        success: true,
        debug: {
          userId: user.id,
          queryDto,
          resultCount: result.items.length,
          meta: result.meta,
          firstItem: result.items[0] || null
        },
        result
      };
    } catch (error) {
      console.error('Debug error:', error);
      return {
        success: false,
        error: error.message,
        stack: error.stack
      };
    }
  }
}
