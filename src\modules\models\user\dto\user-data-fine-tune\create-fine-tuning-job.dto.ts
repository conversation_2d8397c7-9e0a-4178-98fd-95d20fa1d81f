import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString, IsUUID, IsNumber, IsEnum, Min, Max, ValidateIf } from 'class-validator';
import { Type } from 'class-transformer';
import { ProviderFineTuneEnum } from '@modules/models/constants/provider.enum';

/**
 * DTO cho việc tạo fine-tuning job
 */
export class CreateFineTuningJobDto {
  /**
   * Tên hiển thị cho model fine-tuned
   */
  @ApiProperty({
    description: 'Tên hiển thị cho model fine-tuned',
    example: 'My Custom GPT Model',
    maxLength: 255,
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  /**
   * Mô tả về model fine-tuned (tùy chọn)
   */
  @ApiPropertyOptional({
    description: 'Mô tả về model fine-tuned',
    example: 'Model được fine-tune cho tác vụ customer support',
    maxLength: 1000,
  })
  @IsOptional()
  @IsString()
  description?: string;

  /**
   * ID của dataset để fine-tune
   */
  @ApiProperty({
    description: 'UUID của dataset để fine-tune',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty()
  datasetId: string;

  /**
   * ID của model cơ sở (từ system_models hoặc user_models)
   */
  @ApiProperty({
    description: 'UUID của model cơ sở để fine-tune',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty()
  baseModelId: string;

  /**
   * Loại model (system hoặc user)
   */
  @ApiProperty({
    description: 'Loại model cơ sở',
    enum: ['system', 'user'],
    example: 'system',
  })
  @IsString()
  @IsNotEmpty()
  modelType: 'system' | 'user';

  /**
   * Nhà cung cấp AI
   */
  @ApiProperty({
    description: 'Nhà cung cấp AI',
    enum: ProviderFineTuneEnum,
    example: ProviderFineTuneEnum.OPENAI,
  })
  @IsEnum(ProviderFineTuneEnum)
  provider: ProviderFineTuneEnum;

  /**
   * ID của user key LLM (tùy chọn - nếu user muốn sử dụng key riêng)
   */
  @ApiPropertyOptional({
    description: 'UUID của user key LLM (tùy chọn - nếu user muốn sử dụng key riêng)',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID()
  userKeyLlmId?: string;

  /**
   * Suffix cho tên model (chỉ cho OpenAI)
   */
  @ApiPropertyOptional({
    description: 'Suffix cho tên model (chỉ cho OpenAI)',
    example: 'my-model',
    maxLength: 40,
  })
  @IsOptional()
  @IsString()
  @ValidateIf(o => o.provider === ProviderFineTuneEnum.OPENAI)
  suffix?: string;

  /**
   * Siêu tham số cho quá trình fine-tuning
   */
  @ApiPropertyOptional({
    description: 'Siêu tham số cho quá trình fine-tuning',
    type: 'object',
  })
  @IsOptional()
  hyperparameters?: {
    /**
     * Số epoch để huấn luyện (OpenAI: 1-50, Google: 1-100)
     */
    epochs?: number;
    
    /**
     * Kích thước batch (OpenAI: auto hoặc số, Google: số)
     */
    batchSize?: number | 'auto';
    
    /**
     * Tốc độ học (OpenAI: auto hoặc số, Google: số)
     */
    learningRate?: number | 'auto';
  };

  /**
   * Thông tin Google Cloud (chỉ cho Google AI)
   */
  @ApiPropertyOptional({
    description: 'Thông tin Google Cloud (chỉ cho Google AI)',
    type: 'object',
  })
  @IsOptional()
  @ValidateIf(o => o.provider === ProviderFineTuneEnum.GOOGLE)
  googleCloud?: {
    /**
     * ID của Google Cloud Project
     */
    projectId: string;
    
    /**
     * Location của project (ví dụ: us-central1)
     */
    location?: string;
    
    /**
     * Tên bucket Google Cloud Storage
     */
    bucketName: string;
  };
}

/**
 * DTO cho response khi tạo fine-tuning job
 */
export class CreateFineTuningJobResponseDto {
  /**
   * ID của fine-tuning job
   */
  @ApiProperty({
    description: 'ID của fine-tuning job',
    example: 'ftjob-abc123',
  })
  jobId: string;

  /**
   * Tên của fine-tuning job (cho Google AI)
   */
  @ApiPropertyOptional({
    description: 'Tên của fine-tuning job (cho Google AI)',
    example: 'projects/my-project/locations/us-central1/tuningJobs/123',
  })
  jobName?: string;

  /**
   * Trạng thái của job
   */
  @ApiProperty({
    description: 'Trạng thái của job',
    example: 'queued',
  })
  status: string;

  /**
   * ID của model cơ sở
   */
  @ApiProperty({
    description: 'ID của model cơ sở',
    example: 'gpt-3.5-turbo',
  })
  baseModel: string;

  /**
   * ID của file training đã upload (cho OpenAI)
   */
  @ApiPropertyOptional({
    description: 'ID của file training đã upload (cho OpenAI)',
    example: 'file-abc123',
  })
  trainingFileId?: string;

  /**
   * URI của file training trong GCS (cho Google AI)
   */
  @ApiPropertyOptional({
    description: 'URI của file training trong GCS (cho Google AI)',
    example: 'gs://my-bucket/training-data.jsonl',
  })
  trainingDataUri?: string;

  /**
   * Số lượng token đã tính toán
   */
  @ApiProperty({
    description: 'Số lượng token đã tính toán',
    example: 50000,
  })
  estimatedTokens: number;

  /**
   * Chi phí đã trừ (R-Points)
   */
  @ApiProperty({
    description: 'Chi phí đã trừ (R-Points)',
    example: 100,
  })
  costDeducted: number;

  /**
   * Số dư R-Points còn lại
   */
  @ApiProperty({
    description: 'Số dư R-Points còn lại',
    example: 9900,
  })
  remainingBalance: number;

  /**
   * ID của user key LLM đã sử dụng (nếu có)
   */
  @ApiPropertyOptional({
    description: 'ID của user key LLM đã sử dụng (nếu có)',
    example: '123e4567-e89b-12d3-a456-************',
  })
  userKeyLlmId?: string;

  /**
   * Tên của user key LLM đã sử dụng (nếu có)
   */
  @ApiPropertyOptional({
    description: 'Tên của user key LLM đã sử dụng (nếu có)',
    example: 'My OpenAI Key',
  })
  userKeyLlmName?: string;

  /**
   * Thời gian tạo job (epoch millis)
   */
  @ApiProperty({
    description: 'Thời gian tạo job (epoch millis)',
    example: 1640995200000,
  })
  createdAt: number;
}
