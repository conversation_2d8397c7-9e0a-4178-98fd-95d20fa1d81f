import { Injectable, Logger } from '@nestjs/common';
import { UserShopInfoRepository } from '@modules/business/repositories/user-shop-info.repository';
import { UserShopInfoDto, UserShopInfoResponseDto, UpdateUserShopInfoDto } from '../dto/user-shop-info.dto';
import { plainToInstance } from 'class-transformer';
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { Transactional } from 'typeorm-transactional';

/**
 * Service xử lý thông tin cửa hàng của người dùng
 */
@Injectable()
export class UserShopInfoService {
  private readonly logger = new Logger(UserShopInfoService.name);

  constructor(
    private readonly userShopInfoRepository: UserShopInfoRepository,
  ) {}

  /**
   * Lấy thông tin shop của user
   * @param userId ID người dùng
   * @returns Thông tin shop
   */
  async getShopInfo(userId: number): Promise<UserShopInfoResponseDto | null> {
    try {
      this.logger.log(`Lấy thông tin shop cho userId: ${userId}`);
      
      const shopInfo = await this.userShopInfoRepository.findByUserId(userId);
      
      if (!shopInfo) {
        return null;
      }

      return plainToInstance(UserShopInfoResponseDto, shopInfo, { excludeExtraneousValues: true });
    } catch (error) {
      this.logger.error(`Lỗi khi lấy thông tin shop cho userId ${userId}: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.ORDER_FIND_FAILED,
        `Lỗi khi lấy thông tin shop: ${error.message}`
      );
    }
  }

  /**
   * Tạo shop mới
   * @param userId ID người dùng
   * @param shopInfoDto Dữ liệu shop
   * @returns Thông tin shop đã tạo
   */
  @Transactional()
  async createShopInfo(userId: number, shopInfoDto: UserShopInfoDto): Promise<UserShopInfoResponseDto> {
    try {
      this.logger.log(`Tạo shop mới cho userId: ${userId}`);

      const shopInfo = await this.userShopInfoRepository.create({
        userId,
        shopName: shopInfoDto.shopName,
        shopPhone: shopInfoDto.shopPhone,
        shopAddress: shopInfoDto.shopAddress,
        shopProvince: shopInfoDto.shopProvince,
        shopDistrict: shopInfoDto.shopDistrict,
        shopWard: shopInfoDto.shopWard,
      });

      this.logger.log(`Đã tạo shop mới với ID: ${shopInfo.id} cho userId: ${userId}`);
      return plainToInstance(UserShopInfoResponseDto, shopInfo, { excludeExtraneousValues: true });
    } catch (error) {
      this.logger.error(`Lỗi khi tạo shop mới cho userId ${userId}: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.ORDER_CREATE_FAILED,
        `Lỗi khi tạo shop mới: ${error.message}`
      );
    }
  }

  /**
   * Tạo hoặc cập nhật thông tin shop (backward compatibility)
   * @param userId ID người dùng
   * @param shopInfoDto Dữ liệu shop
   * @returns Thông tin shop đã lưu
   */
  @Transactional()
  async upsertShopInfo(userId: number, shopInfoDto: UserShopInfoDto): Promise<UserShopInfoResponseDto> {
    try {
      this.logger.log(`Upsert thông tin shop cho userId: ${userId}`);

      const shopInfo = await this.userShopInfoRepository.upsert(userId, {
        userId,
        shopName: shopInfoDto.shopName,
        shopPhone: shopInfoDto.shopPhone,
        shopAddress: shopInfoDto.shopAddress,
        shopProvince: shopInfoDto.shopProvince,
        shopDistrict: shopInfoDto.shopDistrict,
        shopWard: shopInfoDto.shopWard,
      });

      this.logger.log(`Đã upsert thông tin shop cho userId: ${userId}`);
      return plainToInstance(UserShopInfoResponseDto, shopInfo, { excludeExtraneousValues: true });
    } catch (error) {
      this.logger.error(`Lỗi khi upsert thông tin shop cho userId ${userId}: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.ORDER_CREATE_FAILED,
        `Lỗi khi lưu thông tin shop: ${error.message}`
      );
    }
  }

  /**
   * Cập nhật thông tin shop theo ID
   * @param shopId ID shop
   * @param userId ID người dùng
   * @param updateDto Dữ liệu cập nhật
   * @returns Thông tin shop đã cập nhật
   */
  @Transactional()
  async updateShopInfoById(shopId: number, userId: number, updateDto: UpdateUserShopInfoDto): Promise<UserShopInfoResponseDto> {
    try {
      this.logger.log(`Cập nhật shop với ID: ${shopId} cho userId: ${userId}`);

      // Kiểm tra shop có tồn tại và thuộc về user không
      const existingShopInfo = await this.userShopInfoRepository.findByIdAndUserId(shopId, userId);
      if (!existingShopInfo) {
        throw new AppException(
          BUSINESS_ERROR_CODES.ORDER_NOT_FOUND,
          `Shop với ID ${shopId} không tồn tại hoặc không thuộc về bạn`
        );
      }

      const shopInfo = await this.userShopInfoRepository.updateById(shopId, userId, updateDto);

      this.logger.log(`Đã cập nhật shop với ID: ${shopId} cho userId: ${userId}`);
      return plainToInstance(UserShopInfoResponseDto, shopInfo, { excludeExtraneousValues: true });
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật shop với ID ${shopId} cho userId ${userId}: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.ORDER_UPDATE_FAILED,
        `Lỗi khi cập nhật thông tin shop: ${error.message}`
      );
    }
  }

  /**
   * Cập nhật thông tin shop (backward compatibility)
   * @param userId ID người dùng
   * @param updateDto Dữ liệu cập nhật
   * @returns Thông tin shop đã cập nhật
   */
  @Transactional()
  async updateShopInfo(userId: number, updateDto: UpdateUserShopInfoDto): Promise<UserShopInfoResponseDto> {
    try {
      this.logger.log(`Cập nhật thông tin shop cho userId: ${userId}`);

      // Kiểm tra shop info có tồn tại không
      const existingShopInfo = await this.userShopInfoRepository.findByUserId(userId);
      if (!existingShopInfo) {
        throw new AppException(
          BUSINESS_ERROR_CODES.ORDER_NOT_FOUND,
          'Thông tin shop không tồn tại. Vui lòng tạo mới trước.'
        );
      }

      const shopInfo = await this.userShopInfoRepository.updateByUserId(userId, updateDto);

      this.logger.log(`Đã cập nhật thông tin shop cho userId: ${userId}`);
      return plainToInstance(UserShopInfoResponseDto, shopInfo, { excludeExtraneousValues: true });
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật thông tin shop cho userId ${userId}: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.ORDER_UPDATE_FAILED,
        `Lỗi khi cập nhật thông tin shop: ${error.message}`
      );
    }
  }

  /**
   * Xóa shop theo ID
   * @param shopId ID shop
   * @param userId ID người dùng
   * @returns Kết quả xóa
   */
  @Transactional()
  async deleteShopInfoById(shopId: number, userId: number): Promise<boolean> {
    try {
      this.logger.log(`Xóa shop với ID: ${shopId} cho userId: ${userId}`);

      // Kiểm tra shop có tồn tại và thuộc về user không
      const existingShopInfo = await this.userShopInfoRepository.findByIdAndUserId(shopId, userId);
      if (!existingShopInfo) {
        throw new AppException(
          BUSINESS_ERROR_CODES.ORDER_NOT_FOUND,
          `Shop với ID ${shopId} không tồn tại hoặc không thuộc về bạn`
        );
      }

      const deleted = await this.userShopInfoRepository.deleteById(shopId, userId);

      if (!deleted) {
        throw new AppException(
          BUSINESS_ERROR_CODES.ORDER_NOT_FOUND,
          'Không thể xóa shop'
        );
      }

      this.logger.log(`Đã xóa shop với ID: ${shopId} cho userId: ${userId}`);
      return true;
    } catch (error) {
      this.logger.error(`Lỗi khi xóa shop với ID ${shopId} cho userId ${userId}: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.ORDER_DELETION_FAILED,
        `Lỗi khi xóa shop: ${error.message}`
      );
    }
  }

  /**
   * Xóa thông tin shop (backward compatibility)
   * @param userId ID người dùng
   * @returns Kết quả xóa
   */
  @Transactional()
  async deleteShopInfo(userId: number): Promise<boolean> {
    try {
      this.logger.log(`Xóa thông tin shop cho userId: ${userId}`);

      const deleted = await this.userShopInfoRepository.deleteByUserId(userId);

      if (!deleted) {
        throw new AppException(
          BUSINESS_ERROR_CODES.ORDER_NOT_FOUND,
          'Không tìm thấy thông tin shop để xóa'
        );
      }

      this.logger.log(`Đã xóa thông tin shop cho userId: ${userId}`);
      return true;
    } catch (error) {
      this.logger.error(`Lỗi khi xóa thông tin shop cho userId ${userId}: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.ORDER_DELETION_FAILED,
        `Lỗi khi xóa thông tin shop: ${error.message}`
      );
    }
  }

  /**
   * Xóa nhiều shop theo danh sách ID
   * @param shopIds Danh sách ID shop
   * @param userId ID người dùng
   * @returns Kết quả xóa với thông tin chi tiết
   */
  @Transactional()
  async deleteMultipleShops(shopIds: number[], userId: number): Promise<{ deletedCount: number; failedIds: number[] }> {
    try {
      this.logger.log(`Xóa nhiều shop với IDs: ${shopIds.join(', ')} cho userId: ${userId}`);

      const result = await this.userShopInfoRepository.deleteMultiple(shopIds, userId);

      this.logger.log(`Đã xóa ${result.deletedCount}/${shopIds.length} shop cho userId: ${userId}`);
      return result;
    } catch (error) {
      this.logger.error(`Lỗi khi xóa nhiều shop cho userId ${userId}: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.ORDER_DELETION_FAILED,
        `Lỗi khi xóa nhiều shop: ${error.message}`
      );
    }
  }

  /**
   * Lấy tên shop hoặc giá trị mặc định theo shopId
   * @param shopId ID shop
   * @param userId ID người dùng
   * @returns Tên shop
   */
  async getShopNameByShopId(shopId: number, userId: number): Promise<string> {
    try {
      const shopInfo = await this.userShopInfoRepository.findByIdAndUserId(shopId, userId);
      return shopInfo?.shopName || 'Cửa hàng';
    } catch (error) {
      this.logger.warn(`Không thể lấy tên shop cho shopId ${shopId}, userId ${userId}: ${error.message}`);
      return 'Cửa hàng';
    }
  }

  /**
   * Lấy tên shop hoặc giá trị mặc định (backward compatibility)
   * @param userId ID người dùng
   * @returns Tên shop
   */
  async getShopName(userId: number): Promise<string> {
    try {
      const shopInfo = await this.userShopInfoRepository.findByUserId(userId);
      return shopInfo?.shopName || 'Cửa hàng';
    } catch (error) {
      this.logger.warn(`Không thể lấy tên shop cho userId ${userId}: ${error.message}`);
      return 'Cửa hàng';
    }
  }

  /**
   * Lấy số điện thoại shop theo shopId
   * @param shopId ID shop
   * @param userId ID người dùng
   * @returns Số điện thoại shop
   */
  async getShopPhoneByShopId(shopId: number, userId: number): Promise<string> {
    try {
      const shopInfo = await this.userShopInfoRepository.findByIdAndUserId(shopId, userId);
      return shopInfo?.shopPhone || '0123456789';
    } catch (error) {
      this.logger.warn(`Không thể lấy số điện thoại shop cho shopId ${shopId}, userId ${userId}: ${error.message}`);
      return '0123456789';
    }
  }

  /**
   * Lấy số điện thoại shop hoặc giá trị mặc định (backward compatibility)
   * @param userId ID người dùng
   * @returns Số điện thoại shop
   */
  async getShopPhone(userId: number): Promise<string> {
    try {
      const shopInfo = await this.userShopInfoRepository.findByUserId(userId);
      return shopInfo?.shopPhone || '0123456789';
    } catch (error) {
      this.logger.warn(`Không thể lấy số điện thoại shop cho userId ${userId}: ${error.message}`);
      return '0123456789';
    }
  }

  /**
   * Lấy địa chỉ shop hoặc giá trị mặc định
   * @param userId ID người dùng
   * @returns Địa chỉ shop
   */
  async getShopAddress(userId: number): Promise<string> {
    try {
      const shopInfo = await this.userShopInfoRepository.findByUserId(userId);
      return shopInfo?.shopAddress || 'Số 1, Quận 1, TP.HCM';
    } catch (error) {
      this.logger.warn(`Không thể lấy địa chỉ shop cho userId ${userId}: ${error.message}`);
      return 'Số 1, Quận 1, TP.HCM';
    }
  }

  /**
   * Lấy tỉnh/thành phố shop hoặc giá trị mặc định
   * @param userId ID người dùng
   * @returns Tỉnh/thành phố shop
   */
  async getShopProvince(userId: number): Promise<string> {
    try {
      const shopInfo = await this.userShopInfoRepository.findByUserId(userId);
      return shopInfo?.shopProvince || 'Hồ Chí Minh';
    } catch (error) {
      this.logger.warn(`Không thể lấy tỉnh shop cho userId ${userId}: ${error.message}`);
      return 'Hồ Chí Minh';
    }
  }

  /**
   * Lấy quận/huyện shop hoặc giá trị mặc định
   * @param userId ID người dùng
   * @returns Quận/huyện shop
   */
  async getShopDistrict(userId: number): Promise<string> {
    try {
      const shopInfo = await this.userShopInfoRepository.findByUserId(userId);
      return shopInfo?.shopDistrict || 'Quận 1';
    } catch (error) {
      this.logger.warn(`Không thể lấy quận shop cho userId ${userId}: ${error.message}`);
      return 'Quận 1';
    }
  }

  /**
   * Lấy phường/xã shop hoặc giá trị mặc định
   * @param userId ID người dùng
   * @returns Phường/xã shop
   */
  async getShopWard(userId: number): Promise<string> {
    try {
      const shopInfo = await this.userShopInfoRepository.findByUserId(userId);
      return shopInfo?.shopWard || 'Phường Bến Nghé';
    } catch (error) {
      this.logger.warn(`Không thể lấy phường shop cho userId ${userId}: ${error.message}`);
      return 'Phường Bến Nghé';
    }
  }

  /**
   * Lấy danh sách tất cả shop của user
   * @param userId ID người dùng
   * @returns Danh sách shop
   */
  async getAllShops(userId: number): Promise<UserShopInfoResponseDto[]> {
    try {
      this.logger.log(`Lấy danh sách shop cho userId: ${userId}`);

      const shops = await this.userShopInfoRepository.findAllByUserId(userId);

      return shops.map(shop =>
        plainToInstance(UserShopInfoResponseDto, shop, { excludeExtraneousValues: true })
      );
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách shop cho userId ${userId}: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.ORDER_FIND_FAILED,
        `Lỗi khi lấy danh sách shop: ${error.message}`
      );
    }
  }

  /**
   * Lấy thông tin shop theo ID
   * @param shopId ID shop
   * @param userId ID người dùng
   * @returns Thông tin shop
   */
  async getShopById(shopId: number, userId: number): Promise<UserShopInfoResponseDto | null> {
    try {
      this.logger.log(`Lấy thông tin shop với ID: ${shopId} cho userId: ${userId}`);

      const shopInfo = await this.userShopInfoRepository.findByIdAndUserId(shopId, userId);

      if (!shopInfo) {
        return null;
      }

      return plainToInstance(UserShopInfoResponseDto, shopInfo, { excludeExtraneousValues: true });
    } catch (error) {
      this.logger.error(`Lỗi khi lấy thông tin shop với ID ${shopId} cho userId ${userId}: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.ORDER_FIND_FAILED,
        `Lỗi khi lấy thông tin shop: ${error.message}`
      );
    }
  }

  /**
   * Lấy địa chỉ shop theo shopId
   * @param shopId ID shop
   * @param userId ID người dùng
   * @returns Địa chỉ shop
   */
  async getShopAddressByShopId(shopId: number, userId: number): Promise<string> {
    try {
      const shopInfo = await this.userShopInfoRepository.findByIdAndUserId(shopId, userId);
      return shopInfo?.shopAddress || 'Số 123, Đường ABC, Phường 1, Quận 1, TP.HCM';
    } catch (error) {
      this.logger.warn(`Không thể lấy địa chỉ shop cho shopId ${shopId}, userId ${userId}: ${error.message}`);
      return 'Số 123, Đường ABC, Phường 1, Quận 1, TP.HCM';
    }
  }

  /**
   * Lấy tỉnh/thành phố shop theo shopId
   * @param shopId ID shop
   * @param userId ID người dùng
   * @returns Tỉnh/thành phố
   */
  async getShopProvinceByShopId(shopId: number, userId: number): Promise<string> {
    try {
      const shopInfo = await this.userShopInfoRepository.findByIdAndUserId(shopId, userId);
      return shopInfo?.shopProvince || 'Hồ Chí Minh';
    } catch (error) {
      this.logger.warn(`Không thể lấy tỉnh shop cho shopId ${shopId}, userId ${userId}: ${error.message}`);
      return 'Hồ Chí Minh';
    }
  }

  /**
   * Lấy quận/huyện shop theo shopId
   * @param shopId ID shop
   * @param userId ID người dùng
   * @returns Quận/huyện
   */
  async getShopDistrictByShopId(shopId: number, userId: number): Promise<string> {
    try {
      const shopInfo = await this.userShopInfoRepository.findByIdAndUserId(shopId, userId);
      return shopInfo?.shopDistrict || 'Quận 1';
    } catch (error) {
      this.logger.warn(`Không thể lấy quận shop cho shopId ${shopId}, userId ${userId}: ${error.message}`);
      return 'Quận 1';
    }
  }

  /**
   * Lấy phường/xã shop theo shopId
   * @param shopId ID shop
   * @param userId ID người dùng
   * @returns Phường/xã
   */
  async getShopWardByShopId(shopId: number, userId: number): Promise<string> {
    try {
      const shopInfo = await this.userShopInfoRepository.findByIdAndUserId(shopId, userId);
      return shopInfo?.shopWard || 'Phường Bến Nghé';
    } catch (error) {
      this.logger.warn(`Không thể lấy phường shop cho shopId ${shopId}, userId ${userId}: ${error.message}`);
      return 'Phường Bến Nghé';
    }
  }

  /**
   * Kiểm tra shop info có tồn tại không
   * @param userId ID người dùng
   * @returns True nếu tồn tại
   */
  async hasShopInfo(userId: number): Promise<boolean> {
    try {
      return await this.userShopInfoRepository.exists(userId);
    } catch (error) {
      this.logger.warn(`Không thể kiểm tra shop info cho userId ${userId}: ${error.message}`);
      return false;
    }
  }
}
