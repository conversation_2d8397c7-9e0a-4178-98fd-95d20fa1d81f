/**
 * Debug script để kiểm tra cấu trúc dữ liệu từ agent list query
 * Chạy script này để xem raw data structure từ TypeORM
 */

import { DataSource } from 'typeorm';

export async function debugAgentListQuery(dataSource: DataSource, userId: number) {
  console.log('=== DEBUG AGENT LIST QUERY ===');
  
  try {
    // Tạo query giống hệt trong repository
    const baseQuery = dataSource
      .createQueryBuilder()
      .select([
        'agent.id AS agent_id',
        'agent.name AS agent_name',
        'agent.avatar AS agent_avatar',
        'agentUser.active AS agentUser_active',
        'agentUser.exp AS agentUser_exp',
        'typeAgent.name AS typeAgent_name',
        'agentRank.id AS agentRank_id',
        'agentRank.name AS agentRank_name',
        'agentRank.badge AS agentRank_badge',
        'agent.createdAt AS agent_created_at',
        'agent.updatedAt AS agent_updated_at',
        'agentUser.typeId AS agentUser_type_id',
        'agentUser.userModelId AS agentUser_user_model_id',
        'agentUser.systemModelId AS agentUser_system_model_id',
        'agentUser.modelFineTuneId AS agentUser_model_fine_tune_id',
        'userModel.modelId AS userModel_model_id',
        'systemModel.modelId AS systemModel_model_id',
        'userModelFineTune.modelId AS userModelFineTune_model_id',
        'agentRank.maxExp AS agentRank_max_exp'
      ])
      .from('agents', 'agent')
      .innerJoin('agents_user', 'agentUser', 'agentUser.id = agent.id')
      .leftJoin('type_agents', 'typeAgent', 'agentUser.typeId = typeAgent.id')
      .leftJoin('user_models', 'userModel', 'agentUser.userModelId = userModel.id')
      .leftJoin('system_models', 'systemModel', 'agentUser.systemModelId = systemModel.id')
      .leftJoin('user_model_fine_tune', 'userModelFineTune', 'agentUser.modelFineTuneId = userModelFineTune.id')
      .leftJoin(
        'agents_rank',
        'agentRank',
        'agentUser.exp >= agentRank.minExp AND agentUser.exp < agentRank.maxExp AND agentRank.active = true'
      )
      .where('agentUser.userId = :userId', { userId })
      .andWhere('agent.deletedAt IS NULL')
      .orderBy('agent.createdAt', 'DESC')
      .limit(1);

    // Lấy SQL query để kiểm tra
    const sql = baseQuery.getSql();
    console.log('Generated SQL:', sql);
    
    // Lấy raw data
    const rawData = await baseQuery.getRawMany();
    
    if (rawData.length > 0) {
      console.log('\n=== RAW DATA SAMPLE ===');
      console.log(JSON.stringify(rawData[0], null, 2));
      
      console.log('\n=== RAW DATA KEYS ===');
      console.log(Object.keys(rawData[0]));
      
      console.log('\n=== FIELD TYPES ===');
      Object.entries(rawData[0]).forEach(([key, value]) => {
        console.log(`${key}: ${typeof value} = ${value}`);
      });
    } else {
      console.log('No data found for userId:', userId);
    }
    
  } catch (error) {
    console.error('Debug query failed:', error);
  }
}

// Hàm để test mapping
export function testMapping(rawData: any) {
  console.log('\n=== TESTING MAPPING ===');
  
  // Test các field mapping
  const mappingTests = {
    'agent_id': rawData.agent_id,
    'agent_name': rawData.agent_name,
    'agent_avatar': rawData.agent_avatar,
    'agentUser_active': rawData.agentUser_active,
    'agentUser_exp': rawData.agentUser_exp,
    'typeAgent_name': rawData.typeAgent_name,
    'agentRank_id': rawData.agentRank_id,
    'agentRank_name': rawData.agentRank_name,
    'agentRank_badge': rawData.agentRank_badge,
    'agent_created_at': rawData.agent_created_at,
    'agent_updated_at': rawData.agent_updated_at,
    'agentUser_type_id': rawData.agentUser_type_id,
    'agentUser_user_model_id': rawData.agentUser_user_model_id,
    'agentUser_system_model_id': rawData.agentUser_system_model_id,
    'agentUser_model_fine_tune_id': rawData.agentUser_model_fine_tune_id,
    'userModel_model_id': rawData.userModel_model_id,
    'systemModel_model_id': rawData.systemModel_model_id,
    'userModelFineTune_model_id': rawData.userModelFineTune_model_id,
    'agentRank_max_exp': rawData.agentRank_max_exp
  };
  
  console.log('Mapping test results:');
  Object.entries(mappingTests).forEach(([expectedKey, actualValue]) => {
    const status = actualValue !== undefined ? '✅' : '❌';
    console.log(`${status} ${expectedKey}: ${actualValue}`);
  });
}
