import { AppException } from '@common/exceptions';
import { PaginatedResult } from '@common/response';
import { Agent, AgentUser } from '@modules/agent/entities';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions';
import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { Transactional } from 'typeorm-transactional';
import { AgentQueryDto } from '@modules/agent/user/dto/agent/agent-query.dto';

/**
 * Interface cho raw result từ raw SQL query
 * Field names theo exact SQL output
 */
export interface AgentListRawResult {
  // Fields với alias (AS trong SQL)
  agent_id: string;
  agent_name: string;
  agent_avatar: string | null;
  agentUser_active: boolean;
  agentUser_exp: number;
  typeAgent_name: string | null;
  agentRank_id: number | null;
  agentRank_name: string | null;
  agentRank_badge: string | null;

  // Fields không có alias (tên gốc từ database)
  created_at: string;
  updated_at: string;
  agentUser_type_id: number;
  agentUser_user_model_id: string | null;
  agentUser_system_model_id: string | null;
  agentUser_model_fine_tune_id: string | null;
  userModel_model_id: string | null;
  systemModel_model_id: string | null;
  userModelFineTune_model_id: string | null;
  agentRank_max_exp: number | null;
}

/**
 * Repository cho AgentUser
 * Xử lý các thao tác CRUD và truy vấn nâng cao liên quan đến agent của người dùng
 */
@Injectable()
export class AgentUserRepository extends Repository<AgentUser> {
  private readonly logger = new Logger(AgentUserRepository.name);

  constructor(private dataSource: DataSource) {
    super(AgentUser, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản cho AgentUser
   * @returns SelectQueryBuilder cho AgentUser
   */
  createBaseQuery(): SelectQueryBuilder<AgentUser> {
    return this.createQueryBuilder('agentUser');
  }

  /**
   * Tạo query builder cơ bản cho Agent
   * @returns SelectQueryBuilder cho Agent
   */
  private createAgentBaseQuery(): SelectQueryBuilder<Agent> {
    return this.dataSource
      .getRepository(Agent)
      .createQueryBuilder('agent');
  }

  /**
   * Tìm agent của người dùng theo ID
   * @param id ID của agent
   * @returns AgentUser nếu tìm thấy, null nếu không tìm thấy
   */
  async findById(id: string): Promise<AgentUser | null> {
    return this.createBaseQuery()
      .where('agentUser.id = :id', { id })
      .getOne();
  }

  /**
   * Tìm agent theo ID và userId
   * @param id ID của agent
   * @param userId ID của người dùng
   * @returns Agent và AgentUser nếu tìm thấy
   */
  async findAgentByIdAndUserId(
    id: string,
    userId: number,
  ): Promise<{ agent: Agent; agentUser: AgentUser } | null> {
    // Select tất cả các trường cần thiết từ agent, bao gồm modelConfig
    const agent = await this.createAgentBaseQuery()
      .select([
        'agent.id',
        'agent.name',
        'agent.avatar',
        'agent.modelConfig',
        'agent.instruction',
        'agent.status',
        'agent.vectorStoreId',
        'agent.createdAt',
        'agent.updatedAt'
      ])
      .where('agent.id = :id', { id })
      .andWhere('agent.deletedAt IS NULL')
      .getOne();

    if (!agent) {
      return null;
    }

    // Select tất cả các trường cần thiết từ agentUser với JOIN model tables (theo logic mới)
    const agentUserQuery = this.createBaseQuery()
      .leftJoinAndSelect('agentUser.typeAgent', 'typeAgent')
      .leftJoin('user_models', 'userModel', 'agentUser.user_model_id = userModel.id')
      .leftJoin('system_models', 'systemModel', 'agentUser.system_model_id = systemModel.id')
      .leftJoin('user_model_fine_tune', 'userModelFineTune', 'agentUser.model_fine_tune_id = userModelFineTune.id')
      .addSelect('agentUser.id', 'agentUser_id')
      .addSelect('agentUser.userId', 'agentUser_userId')
      .addSelect('agentUser.typeId', 'agentUser_typeId')
      .addSelect('agentUser.active', 'agentUser_active')
      .addSelect('agentUser.exp', 'agentUser_exp')
      .addSelect('agentUser.strategyId', 'agentUser_strategyId')
      .addSelect('agentUser.profile', 'agentUser_profile')
      .addSelect('agentUser.convertConfig', 'agentUser_convertConfig')
      .addSelect('agentUser.userModelId', 'agentUser_userModelId')
      .addSelect('agentUser.systemModelId', 'agentUser_systemModelId')
      .addSelect('agentUser.modelFineTuneId', 'agentUser_modelFineTuneId')
      // Lấy model_id từ các bảng JOIN (theo logic mới)
      .addSelect('userModel.modelId', 'userModel_modelId')
      .addSelect('systemModel.modelId', 'systemModel_modelId')
      .addSelect('userModelFineTune.modelId', 'userModelFineTune_modelId')
      .where('agentUser.id = :id', { id })
      .andWhere('agentUser.userId = :userId', { userId });

    const rawResult = await agentUserQuery.getRawAndEntities();
    const agentUser = rawResult.entities[0];
    const rawData = rawResult.raw[0];

    if (!agentUser) {
      return null;
    }

    // Resolve model_id từ raw data
    let resolvedModelId = '';
    let resolvedProvider = '';

    // Debug logging để kiểm tra dữ liệu từ raw data
    this.logger.debug(`Debug model resolution for agent ${id}:`, {
      // Từ raw data
      agentUser_userModelId: rawData?.agentUser_userModelId,
      agentUser_systemModelId: rawData?.agentUser_systemModelId,
      agentUser_modelFineTuneId: rawData?.agentUser_modelFineTuneId,
      userModel_modelId: rawData?.userModel_modelId,
      systemModel_modelId: rawData?.systemModel_modelId,
      userModelFineTune_modelId: rawData?.userModelFineTune_modelId,
    });

    // Sử dụng raw data thay vì entity vì entity không được populate đúng
    if (rawData?.agentUser_userModelId && rawData?.userModel_modelId) {
      // Scenario 1: user_model_id != null → JOIN user_models → lấy user_models.model_id
      resolvedModelId = rawData.userModel_modelId;
      resolvedProvider = 'user';
      this.logger.debug(`Using Scenario 1 (user model): ${resolvedModelId}`);
    } else if (rawData?.agentUser_systemModelId && rawData?.systemModel_modelId) {
      // Scenario 2: system_model_id != null → JOIN system_models → lấy system_models.model_id
      resolvedModelId = rawData.systemModel_modelId;
      resolvedProvider = 'system';
      this.logger.debug(`Using Scenario 2 (system model): ${resolvedModelId}`);
    } else if (rawData?.agentUser_modelFineTuneId && rawData?.userModelFineTune_modelId) {
      // Scenario 3: model_fine_tune_id != null → JOIN user_model_fine_tune → lấy user_model_fine_tune.model_id
      resolvedModelId = rawData.userModelFineTune_modelId;
      resolvedProvider = 'fine_tune';
      this.logger.debug(`Using Scenario 3 (fine tune model): ${resolvedModelId}`);
    } else {
      // Scenario 4: Không có model nào → trống
      resolvedModelId = '';
      resolvedProvider = 'unknown';
      this.logger.debug(`Using Scenario 4 (no model): empty`);
    }

    // Populate entity fields từ raw data để service có thể truy cập
    agentUser.userModelId = rawData?.agentUser_userModelId || null;
    agentUser.systemModelId = rawData?.agentUser_systemModelId || null;
    agentUser.modelFineTuneId = rawData?.agentUser_modelFineTuneId || null;

    // Thêm resolved model info vào agentUser
    (agentUser as any).resolvedModelId = resolvedModelId;
    (agentUser as any).resolvedProvider = resolvedProvider;

    return { agent, agentUser };
  }

  /**
   * Cập nhật trạng thái hoạt động của agent
   * @param id ID của agent
   * @param userId ID của người dùng
   * @param active Trạng thái hoạt động mới
   */
  @Transactional()
  async updateAgentActive(
    id: string,
    userId: number,
    active: boolean,
  ): Promise<void> {
    try {
      // Kiểm tra agent có tồn tại không - chỉ select các trường cần thiết
      const agentUser = await this.createBaseQuery()
        .select(['agentUser.id', 'agentUser.userId'])
        .where('agentUser.id = :id', { id })
        .andWhere('agentUser.userId = :userId', { userId })
        .getOne();

      if (!agentUser) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
      }

      // Cập nhật trạng thái hoạt động
      const qb = this.createQueryBuilder()
        .update(AgentUser)
        .set({ active })
        .where('id = :id', { id })
        .andWhere('userId = :userId', { userId });

      const result = await qb.execute();
      if (result.affected === 0) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_UPDATE_FAILED);
      }
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật trạng thái hoạt động của agent: ${error.message}`);
      throw error;
    }
  }

  /**
   * Cập nhật loại agent cho một agent
   * @param id ID của agent
   * @param userId ID của người dùng
   * @param typeId ID loại agent mới
   */
  @Transactional()
  async updateAgentType(
    id: string,
    userId: number,
    typeId: number,
  ): Promise<void> {
    try {
      // Cập nhật loại agent
      const qb = this.createQueryBuilder()
        .update(AgentUser)
        .set({ typeId })
        .where('id = :id', { id })
        .andWhere('userId = :userId', { userId });

      const result = await qb.execute();
      if (result.affected === 0) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
      }
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật loại agent: ${error.message}`);
      throw error;
    }
  }

  /**
   * Xóa mềm agent
   * @param id ID của agent
   * @param userId ID của người dùng
   */
  @Transactional()
  async softDeleteAgent(id: string, userId: number): Promise<void> {
    try {
      // Kiểm tra agent có tồn tại không - chỉ select các trường cần thiết
      const agentExists = await this.createBaseQuery()
        .select(['agentUser.id'])
        .where('agentUser.id = :id', { id })
        .andWhere('agentUser.userId = :userId', { userId })
        .getOne();

      if (!agentExists) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
      }

      // Xóa mềm agent
      const qb = this.dataSource
        .getRepository(Agent)
        .createQueryBuilder()
        .update(Agent)
        .set({ deletedAt: Date.now() })
        .where('id = :id', { id });

      const result = await qb.execute();
      if (result.affected === 0) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_DELETE_FAILED);
      }
    } catch (error) {
      this.logger.error(`Lỗi khi xóa agent: ${error.message}`);
      throw error;
    }
  }

  /**
   * Lấy danh sách agent của người dùng với phân trang - sử dụng raw SQL
   * @param userId ID của người dùng
   * @param queryDto Tham số truy vấn và phân trang
   * @returns Danh sách agent có phân trang
   */
  async getAgentsList(
    userId: number,
    queryDto: AgentQueryDto,
  ): Promise<PaginatedResult<AgentListRawResult>> {
    try {
      // Thực hiện phân trang
      const page = queryDto.page || 1;
      const limit = queryDto.limit || 10;
      const offset = (page - 1) * limit;

      // Xây dựng WHERE conditions
      let whereConditions = 'agentUser.user_id = $1 AND agent.deleted_at IS NULL';
      const queryParams: any[] = [userId];
      let paramIndex = 2;

      // Thêm search condition
      if (queryDto.search) {
        whereConditions += ` AND agent.name ILIKE $${paramIndex}`;
        queryParams.push(`%${queryDto.search}%`);
        paramIndex++;
      }

      // Thêm typeId filter
      if (queryDto.typeId) {
        whereConditions += ` AND agentUser.type_id = $${paramIndex}`;
        queryParams.push(queryDto.typeId);
        paramIndex++;
      }

      // Thêm active filter
      if (queryDto.active !== undefined) {
        whereConditions += ` AND agentUser.active = $${paramIndex}`;
        queryParams.push(queryDto.active);
        paramIndex++;
      }

      // SQL query chính xác như bạn cung cấp
      const mainQuery = `
        SELECT "agent"."id"                       AS "agent_id",
               "agent"."name"                     AS "agent_name",
               "agent"."avatar"                   AS "agent_avatar",
               "agentUser"."active"               AS "agentUser_active",
               "agentUser"."exp"                  AS "agentUser_exp",
               "typeAgent"."name"                 AS "typeAgent_name",
               "agentRank"."id"                   AS "agentRank_id",
               "agentRank"."name"                 AS "agentRank_name",
               "agentRank"."badge"                AS "agentRank_badge",
               "agent"."created_at",
               "agent"."updated_at",
               "agentUser"."type_id"              AS "agentUser_type_id",
               "agentUser"."user_model_id"        AS "agentUser_user_model_id",
               "agentUser"."system_model_id"      AS "agentUser_system_model_id",
               "agentUser"."model_fine_tune_id"   AS "agentUser_model_fine_tune_id",
               "userModel"."model_id"             AS "userModel_model_id",
               "systemModel"."model_id"           AS "systemModel_model_id",
               "userModelFineTune"."model_id"     AS "userModelFineTune_model_id",
               "agentRank"."max_exp"              AS "agentRank_max_exp"
        FROM "agents" "agent"
                 INNER JOIN "agents_user" "agentUser" ON "agentUser"."id" = "agent"."id"
                 LEFT JOIN "type_agents" "typeAgent" ON "agentUser"."type_id" = "typeAgent"."id"
                 LEFT JOIN "user_models" "userModel" ON "agentUser"."user_model_id" = "userModel"."id"
                 LEFT JOIN "system_models" "systemModel" ON "agentUser"."system_model_id" = "systemModel"."id"
                 LEFT JOIN "user_model_fine_tune" "userModelFineTune"
                           ON "agentUser"."model_fine_tune_id" = "userModelFineTune"."id"
                 LEFT JOIN "agents_rank" "agentRank"
                           ON "agentUser"."exp" >= "agentRank"."min_exp" AND "agentUser"."exp" < "agentRank"."max_exp" AND
                              "agentRank"."active" = true
        WHERE ${whereConditions}
        ORDER BY "agent"."created_at" DESC
        LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
      `;

      // Count query
      const countQuery = `
        SELECT COUNT(*) as total
        FROM "agents" "agent"
                 INNER JOIN "agents_user" "agentUser" ON "agentUser"."id" = "agent"."id"
        WHERE ${whereConditions}
      `;

      // Thêm limit và offset vào params
      queryParams.push(limit, offset);

      this.logger.debug('Executing SQL query:', mainQuery);
      this.logger.debug('Query parameters:', queryParams);

      // Thực hiện queries
      const [items, countResult] = await Promise.all([
        this.dataSource.query(mainQuery, queryParams),
        this.dataSource.query(countQuery, queryParams.slice(0, -2)) // Loại bỏ limit và offset cho count query
      ]);

      const total = parseInt(countResult[0]?.total || '0');

      // Debug: Log raw data để kiểm tra cấu trúc
      if (items.length > 0) {
        this.logger.debug('Raw query result sample:', JSON.stringify(items[0], null, 2));
        this.logger.debug('Raw query result keys:', Object.keys(items[0]));
      }

      return {
        items,
        meta: {
          totalItems: total,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(total / limit),
          currentPage: page,
          hasItems: total > 0,
        },
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách agent: ${error.message}`, error.stack);
      throw new AppException(
        AGENT_ERROR_CODES.AGENT_LIST_QUERY_FAILED,
        'Không thể lấy danh sách agent'
      );
    }
  }

  /**
   * Cập nhật Strategy ID cho agent user
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @param strategyId ID của strategy
   */
  async updateStrategyId(agentId: string, userId: number, strategyId: string): Promise<void> {
    try {
      const result = await this.createQueryBuilder()
        .update(AgentUser)
        .set({ strategyId })
        .where('id = :agentId', { agentId })
        .andWhere('userId = :userId', { userId })
        .execute();

      if (result.affected === 0) {
        this.logger.warn(`Không tìm thấy agent user ${agentId} cho user ${userId} để cập nhật strategy`);
      }
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật strategy cho agent user ${agentId}: ${error.message}`, error.stack);
      throw error;
    }
  }
}
