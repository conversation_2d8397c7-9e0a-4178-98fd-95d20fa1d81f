import { Controller, Get, Post, Put, Delete, Body, UseGuards, Logger, Param } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiParam } from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@modules/auth/guards/jwt.util';
import { UserShopInfoService } from '@modules/business/user/services';
import { UserShopInfoDto, UserShopInfoResponseDto, UpdateUserShopInfoDto, DeleteMultipleShopsDto } from '../dto/user-shop-info.dto';

/**
 * Controller xử lý thông tin cửa hàng của người dùng
 */
@ApiTags('User Shop Info')
@Controller('user/shop-info')
@UseGuards(JwtUserGuard)
@ApiBearerAuth("JWT-auth")
export class UserShopInfoController {
  private readonly logger = new Logger(UserShopInfoController.name);

  constructor(
    private readonly userShopInfoService: UserShopInfoService,
  ) {}

  /**
   * Tạo shop mới
   */
  @Post('create')
  @ApiOperation({
    summary: 'Tạo shop mới',
    description: 'Tạo cửa hàng mới cho người dùng'
  })
  @ApiResponse({
    status: 201,
    description: 'Shop đã được tạo thành công',
    type: UserShopInfoResponseDto
  })
  @ApiResponse({
    status: 400,
    description: 'Dữ liệu không hợp lệ'
  })
  async createShopInfo(
    @Body() shopInfoDto: UserShopInfoDto,
    @CurrentUser() user: JwtPayload
  ) {
    try {
      this.logger.log(`User ${user.id} tạo shop mới`);

      const shopInfo = await this.userShopInfoService.createShopInfo(user.id, shopInfoDto);

      return {
        success: true,
        message: 'Tạo shop mới thành công',
        data: shopInfo
      };
    } catch (error) {
      this.logger.error(`Lỗi khi tạo shop mới cho user ${user.id}:`, error);
      throw error;
    }
  }



  /**
   * Cập nhật shop theo ID
   */
  @Put(':id')
  @ApiOperation({
    summary: 'Cập nhật shop theo ID',
    description: 'Cập nhật thông tin cửa hàng theo ID'
  })
  @ApiParam({
    name: 'id',
    description: 'ID của shop',
    example: 1
  })
  @ApiResponse({
    status: 200,
    description: 'Shop đã được cập nhật thành công',
    type: UserShopInfoResponseDto
  })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy shop hoặc shop không thuộc về bạn'
  })
  @ApiResponse({
    status: 400,
    description: 'Dữ liệu không hợp lệ'
  })
  async updateShopInfoById(
    @Param('id') shopId: number,
    @Body() updateDto: UpdateUserShopInfoDto,
    @CurrentUser() user: JwtPayload
  ) {
    try {
      this.logger.log(`User ${user.id} cập nhật shop ${shopId}`);

      const shopInfo = await this.userShopInfoService.updateShopInfoById(shopId, user.id, updateDto);

      return {
        success: true,
        message: 'Cập nhật shop thành công',
        data: shopInfo
      };
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật shop ${shopId} cho user ${user.id}:`, error);
      throw error;
    }
  }



  /**
   * Xóa shop theo ID
   */
  @Delete(':id')
  @ApiOperation({
    summary: 'Xóa shop theo ID',
    description: 'Xóa cửa hàng theo ID'
  })
  @ApiParam({
    name: 'id',
    description: 'ID của shop',
    example: 1
  })
  @ApiResponse({
    status: 200,
    description: 'Shop đã được xóa thành công'
  })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy shop hoặc shop không thuộc về bạn'
  })
  async deleteShopInfoById(
    @Param('id') shopId: number,
    @CurrentUser() user: JwtPayload
  ) {
    try {
      this.logger.log(`User ${user.id} xóa shop ${shopId}`);

      await this.userShopInfoService.deleteShopInfoById(shopId, user.id);

      return {
        success: true,
        message: 'Xóa shop thành công'
      };
    } catch (error) {
      this.logger.error(`Lỗi khi xóa shop ${shopId} cho user ${user.id}:`, error);
      throw error;
    }
  }

  /**
   * Xóa nhiều shop theo danh sách ID
   */
  @Delete('multiple')
  @ApiOperation({
    summary: 'Xóa nhiều shop',
    description: 'Xóa nhiều cửa hàng theo danh sách ID'
  })
  @ApiResponse({
    status: 200,
    description: 'Các shop đã được xóa thành công',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'Xóa các shop thành công' },
        data: {
          type: 'object',
          properties: {
            deletedCount: { type: 'number', example: 3 },
            failedIds: { type: 'array', items: { type: 'number' }, example: [] }
          }
        }
      }
    }
  })
  @ApiResponse({
    status: 400,
    description: 'Dữ liệu không hợp lệ'
  })
  async deleteMultipleShops(
    @Body() deleteDto: DeleteMultipleShopsDto,
    @CurrentUser() user: JwtPayload
  ) {
    try {
      this.logger.log(`User ${user.id} xóa nhiều shop: ${deleteDto.shopIds.join(', ')}`);

      const result = await this.userShopInfoService.deleteMultipleShops(deleteDto.shopIds, user.id);

      return {
        success: true,
        message: 'Xóa các shop thành công',
        data: result
      };
    } catch (error) {
      this.logger.error(`Lỗi khi xóa nhiều shop cho user ${user.id}:`, error);
      throw error;
    }
  }



  /**
   * Lấy danh sách tất cả shop của user (API mới)
   */
  @Get('all')
  @ApiOperation({
    summary: 'Lấy danh sách shop',
    description: 'Lấy danh sách tất cả cửa hàng của người dùng hiện tại'
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách shop thành công',
    type: [UserShopInfoResponseDto]
  })
  async getAllShops(@CurrentUser() user: JwtPayload) {
    try {
      this.logger.log(`User ${user.id} lấy danh sách shop`);

      const shops = await this.userShopInfoService.getAllShops(user.id);

      return {
        success: true,
        message: 'Lấy danh sách shop thành công',
        data: shops
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách shop cho user ${user.id}:`, error);
      throw error;
    }
  }

  /**
   * Lấy thông tin shop theo ID (API mới)
   */
  @Get('shop/:id')
  @ApiOperation({
    summary: 'Lấy thông tin shop theo ID',
    description: 'Lấy thông tin cửa hàng theo ID'
  })
  @ApiParam({
    name: 'id',
    description: 'ID của shop',
    example: 1
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy thông tin shop thành công',
    type: UserShopInfoResponseDto
  })
  async getShopById(
    @CurrentUser() user: JwtPayload,
    @Param('id') shopId: number
  ) {
    try {
      this.logger.log(`User ${user.id} lấy thông tin shop ${shopId}`);

      const shopInfo = await this.userShopInfoService.getShopById(shopId, user.id);
      if (!shopInfo) {
        return {
          success: false,
          message: `Shop với ID ${shopId} không tồn tại hoặc không thuộc về bạn`,
          data: null
        };
      }

      return {
        success: true,
        message: 'Lấy thông tin shop thành công',
        data: shopInfo
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy thông tin shop ${shopId} cho user ${user.id}:`, error);
      throw error;
    }
  }

  /**
   * Kiểm tra thông tin cửa hàng có tồn tại không
   */
  @Get('exists')
  @ApiOperation({
    summary: 'Kiểm tra thông tin cửa hàng',
    description: 'Kiểm tra xem người dùng đã có thông tin cửa hàng chưa'
  })
  @ApiResponse({
    status: 200,
    description: 'Kết quả kiểm tra',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'Kiểm tra thành công' },
        data: {
          type: 'object',
          properties: {
            exists: { type: 'boolean', example: true },
            hasCompleteInfo: { type: 'boolean', example: true }
          }
        }
      }
    }
  })
  async checkShopInfoExists(@CurrentUser() user: JwtPayload) {
    try {
      this.logger.log(`User ${user.id} kiểm tra thông tin shop`);

      const exists = await this.userShopInfoService.hasShopInfo(user.id);
      const shopInfo = exists ? await this.userShopInfoService.getShopInfo(user.id) : null;

      // Kiểm tra thông tin có đầy đủ không
      const hasCompleteInfo = shopInfo &&
        shopInfo.shopName &&
        shopInfo.shopPhone &&
        shopInfo.shopAddress &&
        shopInfo.shopProvince &&
        shopInfo.shopDistrict;

      return {
        success: true,
        message: 'Kiểm tra thành công',
        data: {
          exists,
          hasCompleteInfo: !!hasCompleteInfo
        }
      };
    } catch (error) {
      this.logger.error(`Lỗi khi kiểm tra thông tin shop cho user ${user.id}:`, error);
      throw error;
    }
  }
}
