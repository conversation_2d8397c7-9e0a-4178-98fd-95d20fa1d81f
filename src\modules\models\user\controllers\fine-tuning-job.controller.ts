import { Controller, Post, Body, UseGuards, HttpStatus } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { FineTuningJobService } from '../services/fine-tuning-job.service';
import { CreateFineTuningJobDto, CreateFineTuningJobResponseDto } from '../dto/user-data-fine-tune/create-fine-tuning-job.dto';
import { MODELS_ERROR_CODES } from '@modules/models/exceptions/models.exception';
import { JwtUserGuard } from '@/modules/auth/guards';
import { ApiResponseDto } from '@/common/response';
import { CurrentUser } from '@/modules/auth/decorators';
import { ApiErrorResponse } from '@/common/decorators/api-error-response.decorator';

/**
 * Controller xử lý các API liên quan đến fine-tuning jobs
 */
@ApiTags('User Fine-Tuning Jobs')
@Controller('user/fine-tuning-jobs')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class FineTuningJobController {
  constructor(private readonly fineTuningJobService: FineTuningJobService) {}

  /**
   * Tạo fine-tuning job mới
   */
  @Post()
  @ApiOperation({
    summary: 'Tạo fine-tuning job mới',
    description: `
    Tạo fine-tuning job mới cho OpenAI hoặc Google AI.
    
    **Quy trình:**
    1. Validate dataset và model cơ sở
    2. Tính toán token và chi phí
    3. Trừ R-Points từ tài khoản user
    4. Upload file training lên provider
    5. Tạo fine-tuning job
    6. Lưu thông tin vào database
    
    **Lưu ý:**
    - Nếu cung cấp userKeyLlmId, sẽ sử dụng API key riêng của user
    - Nếu không có userKeyLlmId, sẽ sử dụng system API key
    - Chi phí được tính dựa trên số token và trainingPricing của model
    - User phải có đủ R-Points để thực hiện fine-tuning
    `,
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Fine-tuning job đã được tạo thành công',
    type: ApiResponseDto<CreateFineTuningJobResponseDto>,
  })
  @ApiErrorResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Dữ liệu đầu vào không hợp lệ',
    schema: {
      example: {
        code: MODELS_ERROR_CODES.INVALID_INPUT.code,
        message: 'Dữ liệu đầu vào không hợp lệ',
        path: '/v1/user/fine-tuning-jobs',
        requestId: 'req-123',
      },
    },
  })
  @ApiErrorResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy dataset hoặc model',
    schema: {
      example: {
        code: MODELS_ERROR_CODES.DATASET_NOT_FOUND.code,
        message: 'Không tìm thấy dataset hoặc bạn không có quyền truy cập',
        path: '/v1/user/fine-tuning-jobs',
        requestId: 'req-123',
      },
    },
  })
  @ApiErrorResponse({
    status: HttpStatus.PAYMENT_REQUIRED,
    description: 'Số dư R-Points không đủ',
    schema: {
      example: {
        code: MODELS_ERROR_CODES.INSUFFICIENT_POINTS.code,
        message: 'Số dư không đủ. Cần 100 R-Point, hiện có 50 R-Point',
        path: '/v1/user/fine-tuning-jobs',
        requestId: 'req-123',
      },
    },
  })
  @ApiErrorResponse({
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    description: 'Lỗi khi tạo fine-tuning job với provider',
    schema: {
      example: {
        code: MODELS_ERROR_CODES.FINE_TUNING_JOB_CREATION_FAILED.code,
        message: 'Không thể tạo fine-tuning job với OpenAI',
        path: '/v1/user/fine-tuning-jobs',
        requestId: 'req-123',
      },
    },
  })
  @ApiErrorResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Lỗi hệ thống',
    schema: {
      example: {
        code: 50000,
        message: 'Lỗi hệ thống không xác định',
        path: '/v1/user/fine-tuning-jobs',
        requestId: 'req-123',
      },
    },
  })
  async createFineTuningJob(
    @CurrentUser('id') userId: number,
    @Body() createDto: CreateFineTuningJobDto,
  ): Promise<ApiResponseDto<CreateFineTuningJobResponseDto>> {
    const result = await this.fineTuningJobService.createFineTuningJob(
      userId,
      createDto,
    );

    return ApiResponseDto.success(
      result,
      'Fine-tuning job đã được tạo thành công',
    );
  }
}
