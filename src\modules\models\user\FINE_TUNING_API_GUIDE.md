# API Fine-Tuning Jobs - Hướng Dẫn Sử Dụng

## Tổng quan

API này cho phép user tạo fine-tuning jobs cho cả OpenAI và Google AI, với khả năng sử dụng API key riêng hoặc system key.

## Endpoint

```
POST /v1/user/fine-tuning-jobs
```

## Authentication

- **Guard**: `JwtUserGuard`
- **Header**: `Authorization: Bearer <JWT_TOKEN>`

## Request Body

### DTO: `CreateFineTuningJobDto`

```typescript
{
  "name": "My Custom GPT Model",                    // Tên model fine-tuned
  "description": "Model cho customer support",      // <PERSON>ô tả (tùy chọn)
  "datasetId": "uuid-dataset",                      // UUID của dataset
  "baseModelId": "uuid-model",                      // UUID của model cơ sở
  "modelType": "system",                            // "system" hoặc "user"
  "provider": "OPENAI",                             // "OPENAI" hoặc "GOOGLE"
  "userKeyLlmId": "uuid-key",                       // UUID của user key (tùy chọn)
  "suffix": "my-model",                             // Suffix cho OpenAI (tùy chọn)
  "hyperparameters": {                              // Siêu tham số (tùy chọn)
    "epochs": 3,
    "batchSize": "auto",
    "learningRate": "auto"
  },
  "googleCloud": {                                  // Chỉ cho Google AI
    "projectId": "my-project-id",
    "location": "us-central1",
    "bucketName": "my-bucket"
  }
}
```

## Response

### Success Response (201)

```typescript
{
  "code": 20100,
  "message": "Fine-tuning job đã được tạo thành công",
  "result": {
    "jobId": "ftjob-abc123",                        // ID của job
    "jobName": "projects/.../tuningJobs/123",      // Tên job (Google AI)
    "status": "queued",                             // Trạng thái job
    "baseModel": "gpt-3.5-turbo",                   // Model cơ sở
    "trainingFileId": "file-abc123",                // File ID (OpenAI)
    "trainingDataUri": "gs://bucket/file.jsonl",   // URI (Google AI)
    "estimatedTokens": 50000,                       // Số token ước tính
    "costDeducted": 100,                            // R-Points đã trừ
    "remainingBalance": 9900,                       // Số dư còn lại
    "userKeyLlmId": "uuid-key",                     // Key đã sử dụng
    "userKeyLlmName": "My OpenAI Key",              // Tên key
    "createdAt": 1640995200000                      // Thời gian tạo
  }
}
```

## Quy trình thực hiện

### 1. Validation
- ✅ Validate dataset tồn tại và thuộc về user
- ✅ Validate model cơ sở (system hoặc user model)
- ✅ Validate provider khớp với model
- ✅ Validate user key LLM (nếu có)

### 2. API Key Resolution
- **Nếu có `userKeyLlmId`**: Sử dụng API key riêng của user
- **Nếu không có**: Sử dụng system API key (fallback)

### 3. Cost Calculation
- Đọc file training từ S3
- Đếm token trong file JSONL
- Tính chi phí dựa trên `trainingPricing` của model
- Công thức: `cost = (totalTokens / 1000) * trainingPricing`

### 4. Points Deduction
- Kiểm tra số dư R-Points của user
- Trừ points trước khi tạo job
- Rollback nếu có lỗi

### 5. Job Creation

#### OpenAI
1. Upload file training lên OpenAI
2. Tạo fine-tuning job với hyperparameters
3. Lưu `trainingFileId` và `jobId`

#### Google AI
1. Upload file lên Google Cloud Storage
2. Tạo tuning job trên Vertex AI
3. Lưu `trainingDataUri` và `jobName`

### 6. Database Storage
- Lưu vào `fine_tune_histories` (metadata chi tiết)
- Lưu vào `user_model_fine_tune` (liên kết với key và history)

## Error Codes

| Code | Message | HTTP Status |
|------|---------|-------------|
| 20200 | Không tìm thấy dataset | 404 |
| 20201 | Dataset không có dữ liệu training | 400 |
| 20202 | Không tìm thấy model | 404 |
| 20203 | Provider không khớp với model | 400 |
| 20204 | Thiếu API key | 400 |
| 20205 | Thiếu Google Cloud config | 400 |
| 20206 | Tính toán token thất bại | 500 |
| 20207 | Số dư R-Points không đủ | 402 |
| 20208 | Tạo fine-tuning job thất bại | 422 |

## Ví dụ sử dụng

### OpenAI với User Key

```bash
curl -X POST /v1/user/fine-tuning-jobs \
  -H "Authorization: Bearer <JWT_TOKEN>" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Customer Support GPT",
    "description": "Fine-tuned for customer support",
    "datasetId": "550e8400-e29b-41d4-a716-446655440000",
    "baseModelId": "660e8400-e29b-41d4-a716-446655440000",
    "modelType": "system",
    "provider": "OPENAI",
    "userKeyLlmId": "770e8400-e29b-41d4-a716-446655440000",
    "suffix": "support-v1",
    "hyperparameters": {
      "epochs": 3,
      "batchSize": 4,
      "learningRate": 0.0001
    }
  }'
```

### Google AI với System Key

```bash
curl -X POST /v1/user/fine-tuning-jobs \
  -H "Authorization: Bearer <JWT_TOKEN>" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Gemini Custom Model",
    "description": "Fine-tuned Gemini for specific tasks",
    "datasetId": "550e8400-e29b-41d4-a716-446655440000",
    "baseModelId": "660e8400-e29b-41d4-a716-446655440000",
    "modelType": "system",
    "provider": "GOOGLE",
    "hyperparameters": {
      "epochs": 5,
      "batchSize": 8,
      "learningRate": 0.00001
    },
    "googleCloud": {
      "projectId": "my-ai-project",
      "location": "us-central1",
      "bucketName": "my-training-data"
    }
  }'
```

## Lưu ý quan trọng

1. **File Format**: Dataset phải ở định dạng JSONL
2. **Cost**: Chi phí được tính theo token và trừ trước khi tạo job
3. **API Key**: User key có ưu tiên cao hơn system key
4. **Provider**: Phải khớp giữa model và provider
5. **Google Cloud**: Cần cấu hình project và bucket
6. **Transaction**: Sử dụng database transaction để đảm bảo consistency

## Monitoring

Sau khi tạo job, user có thể:
- Theo dõi trạng thái job qua `jobId` hoặc `jobName`
- Xem lịch sử trong `fine_tune_histories`
- Sử dụng model sau khi hoàn thành

## Security

- API key được mã hóa trước khi lưu
- Chỉ user sở hữu dataset mới có thể tạo job
- Validate quyền truy cập model và key
- Transaction rollback nếu có lỗi
