import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@config';
import { AppException, ErrorCode } from '@common/exceptions/app.exception';
import { GoogleGenAI } from '@google/genai';
import { firstValueFrom } from 'rxjs';
import * as fs from 'fs';
import * as os from 'os';
import * as path from 'path';
import { S3Service } from '../s3.service';
import {
  GoogleAIModel,
  GoogleAIModelsResponse,
  GoogleAIFineTuningParams,
  GoogleAIFineTuningResponse
} from './interfaces/google-ai.interface';

/**
 * Service tương tác với Google AI API
 */
@Injectable()
export class GoogleAIService {
  private readonly googleAI: GoogleGenAI;
  private readonly logger = new Logger(GoogleAIService.name);
  private readonly apiBaseUrl = 'https://generativelanguage.googleapis.com/v1';

  constructor(
    private readonly httpService: HttpService,
  ) {
  }

  /**
   * Lấy danh sách model từ Google AI sử dụng REST API
   * @param pageSize Số lượng model tối đa trên mỗi trang (mặc định: 50, tối đa: 1000)
   * @param pageToken Mã thông báo trang để lấy trang tiếp theo
   * @param options Tùy chọn bổ sung (apiKey)
   * @returns Danh sách model từ Google AI và mã thông báo trang tiếp theo (nếu có)
   * @throws AppException nếu có lỗi khi lấy danh sách model
   */
  /**
   * Lấy danh sách model từ Google AI sử dụng REST API
   * @param apiKey API key của Google AI
   * @param pageSize Số lượng model tối đa trên mỗi trang (mặc định: 50, tối đa: 1000)
   * @param pageToken Mã thông báo trang để lấy trang tiếp theo
   * @returns Danh sách model từ Google AI
   * @throws AppException nếu có lỗi khi lấy danh sách model
   */
  async getModels(
    apiKey: string,
    pageSize?: number,
    pageToken?: string,
  ): Promise<GoogleAIModel[]> {
    try {
      if (!apiKey) {
        throw new Error('API key is required to list models');
      }

      // Thiết lập timeout cho request
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 seconds timeout

      // Xây dựng URL với các tham số phân trang
      let url = `${this.apiBaseUrl}/models?key=${apiKey}`;

      // Thêm tham số pageSize nếu được cung cấp
      if (pageSize) {
        // Đảm bảo pageSize nằm trong khoảng hợp lệ (1-1000)
        const validPageSize = Math.min(Math.max(1, pageSize), 1000);
        url += `&pageSize=${validPageSize}`;
      }

      // Thêm tham số pageToken nếu được cung cấp
      if (pageToken) {
        url += `&pageToken=${encodeURIComponent(pageToken)}`;
      }

      // Gọi REST API để lấy danh sách model
      const response = await firstValueFrom(
        this.httpService.get<GoogleAIModelsResponse>(url, {
          headers: {
            'Content-Type': 'application/json',
          },
          signal: controller.signal,
        }),
      );

      clearTimeout(timeoutId);

      // Lấy danh sách model và mã thông báo trang tiếp theo từ response
      return response.data.models || [];
    } catch (error: any) {
      this.handleApiError(error, 'lấy danh sách model');
    }
  }

  /**
   * Lấy thông tin chi tiết của một model theo ID
   * @param modelId ID của model cần lấy thông tin (ví dụ: gemini-1.5-flash)
   * @param apiKey API key của Google AI
   * @returns Thông tin chi tiết của model
   * @throws AppException nếu có lỗi khi lấy thông tin model
   */
  async retrieveModel(modelId: string, apiKey: string): Promise<GoogleAIModel> {
    try {
      // Kiểm tra modelId
      if (!modelId) {
        throw new AppException(
          ErrorCode.OPENAI_API_ERROR,
          'Model ID không được để trống',
        );
      }

      // Kiểm tra apiKey
      if (!apiKey) {
        throw new AppException(
          ErrorCode.OPENAI_API_ERROR,
          'API key không được để trống',
        );
      }

      // Thiết lập timeout cho request
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 seconds timeout

      // Xây dựng URL để lấy thông tin chi tiết của model
      const url = `${this.apiBaseUrl}/models/${encodeURIComponent(modelId)}?key=${apiKey}`;

      // Gọi REST API để lấy thông tin chi tiết của model
      const response = await firstValueFrom(
        this.httpService.get<GoogleAIModel>(url, {
          headers: {
            'Content-Type': 'application/json',
          },
          signal: controller.signal,
        }),
      );

      clearTimeout(timeoutId);

      // Lấy thông tin model từ response
      const model = response.data;

      if (!model || !model.name) {
        throw new AppException(
          ErrorCode.OPENAI_API_ERROR,
          `Không tìm thấy model với ID: ${modelId}`,
        );
      }

      this.logger.log(`Retrieved details for model: ${model.name}`);
      return model;
    } catch (error: any) {
      // Xử lý trường hợp không tìm thấy model (404)
      if (error.response && error.response.status === 404) {
        throw new AppException(
          ErrorCode.OPENAI_API_ERROR,
          `Không tìm thấy model với ID: ${modelId}`,
        );
      }

      this.handleApiError(error, `lấy thông tin model ${modelId}`);
    }
  }

  /**
   * Tạo fine-tuning job trên Google AI (Vertex AI)
   * @param params Tham số cho fine-tuning job
   * @param apiKey API key của Google AI
   * @param projectId ID của project Google Cloud
   * @param location Location của project (ví dụ: us-central1)
   * @returns Thông tin về fine-tuning job đã tạo
   * @throws AppException nếu có lỗi khi tạo fine-tuning job
   */
  async createFineTuningJob(
    params: GoogleAIFineTuningParams,
    apiKey: string,
    projectId: string,
    location: string = 'us-central1',
  ): Promise<GoogleAIFineTuningResponse> {
    try {
      // Kiểm tra các tham số bắt buộc
      if (!params.baseModelId) {
        throw new AppException(
          ErrorCode.OPENAI_API_ERROR,
          'baseModelId là bắt buộc',
        );
      }

      if (!params.displayName) {
        throw new AppException(
          ErrorCode.OPENAI_API_ERROR,
          'displayName là bắt buộc',
        );
      }

      if (!params.trainingDataUri) {
        throw new AppException(
          ErrorCode.OPENAI_API_ERROR,
          'trainingDataUri là bắt buộc',
        );
      }

      // Thiết lập timeout cho request
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 60000); // 60 seconds timeout

      // Chuẩn bị tham số cho fine-tuning job
      const requestBody: any = {
        baseModelId: params.baseModelId,
        displayName: params.displayName,
        trainingData: {
          datasetUri: params.trainingDataUri,
        },
      };

      // Thêm các tham số tùy chọn nếu có
      if (params.description) {
        requestBody.description = params.description;
      }

      if (params.validationDataUri) {
        requestBody.validationData = {
          datasetUri: params.validationDataUri,
        };
      }

      if (params.hyperParameters) {
        requestBody.hyperParameters = {};

        if (params.hyperParameters.epochCount !== undefined) {
          requestBody.hyperParameters.epochCount = params.hyperParameters.epochCount;
        }

        if (params.hyperParameters.batchSize !== undefined) {
          requestBody.hyperParameters.batchSize = params.hyperParameters.batchSize;
        }

        if (params.hyperParameters.learningRate !== undefined) {
          requestBody.hyperParameters.learningRate = params.hyperParameters.learningRate;
        }
      }

      // Gọi API để tạo fine-tuning job
      const url = `https://${location}-aiplatform.googleapis.com/v1/projects/${projectId}/locations/${location}/tuningJobs`;

      const response = await firstValueFrom(
        this.httpService.post<GoogleAIFineTuningResponse>(
          url,
          requestBody,
          {
            headers: {
              'Content-Type': 'application/json',
              Authorization: `Bearer ${apiKey}`,
            },
            signal: controller.signal,
          },
        ),
      );

      clearTimeout(timeoutId);

      this.logger.log(`Fine-tuning job created successfully: ${response.data.name}`);
      return response.data;
    } catch (error: any) {
      this.logger.error(
        `Google AI API error creating fine-tuning job: ${error.message}`,
        error.stack,
      );

      // Xử lý lỗi quota exceeded
      if (error.response && error.response.status === 429) {
        throw new AppException(
          ErrorCode.OPENAI_QUOTA_EXCEEDED,
          'Đã vượt quá giới hạn sử dụng Google AI API',
        );
      }

      // Xử lý lỗi timeout
      if (
        error.name === 'TimeoutError' ||
        error.code === 'ETIMEDOUT' ||
        error.message.includes('timeout')
      ) {
        throw new AppException(
          ErrorCode.OPENAI_TIMEOUT,
          'Kết nối đến Google AI API bị gián đoạn hoặc quá thời gian chờ',
        );
      }

      // Xử lý lỗi kết nối
      if (
        error.name === 'NetworkError' ||
        error.code === 'ENOTFOUND' ||
        error.message.includes('network')
      ) {
        throw new AppException(
          ErrorCode.OPENAI_TIMEOUT,
          'Lỗi kết nối đến Google AI API',
        );
      }

      // Xử lý lỗi validation
      if (error.response && error.response.status === 400) {
        const errorMessage = error.response.data?.error?.message || 'Dữ liệu không hợp lệ';
        throw new AppException(
          ErrorCode.OPENAI_API_ERROR,
          'Lỗi validation: ' + errorMessage,
        );
      }

      // Xử lý lỗi xác thực
      if (error.response && (error.response.status === 401 || error.response.status === 403)) {
        throw new AppException(
          ErrorCode.OPENAI_API_ERROR,
          'Lỗi xác thực: ' + (error.response.data?.error?.message || 'Không có quyền truy cập'),
        );
      }

      // Các lỗi khác
      throw new AppException(
        ErrorCode.OPENAI_API_ERROR,
        'Lỗi khi tạo fine-tuning job: ' + error.message,
      );
    }
  }

  /**
   * Xử lý lỗi từ API
   * @param error Lỗi từ API
   * @param action Hành động đang thực hiện
   * @throws AppException với mã lỗi và thông báo phù hợp
   */
  private handleApiError(error: any, action: string): never {
    this.logger.error(
      `Error ${action} from Google AI: ${error.message}`,
      error.stack,
    );

    // Xử lý các lỗi khi kết nối Google AI API
    if (error.response) {
      // Lỗi từ API response
      const status = error.response.status;
      const data = error.response.data;

      if (status === 429) {
        throw new AppException(
          ErrorCode.OPENAI_QUOTA_EXCEEDED,
          'Đã vượt quá giới hạn sử dụng Google AI API',
        );
      }

      if (status === 401 || status === 403) {
        throw new AppException(
          ErrorCode.OPENAI_API_ERROR,
          'Lỗi xác thực API key: ' +
          (data?.error?.message || 'Không có quyền truy cập'),
        );
      }

      throw new AppException(
        ErrorCode.OPENAI_API_ERROR,
        `Lỗi API (${status}): ${data?.error?.message || 'Không xác định'}`,
      );
    }

    if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
      throw new AppException(
        ErrorCode.OPENAI_TIMEOUT,
        'Kết nối đến Google AI API bị gián đoạn hoặc quá thời gian chờ',
      );
    }

    if (error.code === 'ENOTFOUND' || error.message.includes('network')) {
      throw new AppException(
        ErrorCode.OPENAI_TIMEOUT,
        'Lỗi kết nối đến Google AI API',
      );
    }

    // Các lỗi khác
    throw new AppException(
      ErrorCode.OPENAI_API_ERROR,
      `Lỗi khi ${action}: ${error.message}`,
    );
  }
}
